# RS-006: Organization Creation Flow

## Ticket Information

- **Story:** 2.2 - Organization Creation
- **Priority:** High
- **Assignee:** Full-stack Developer
- **Estimate:** 5 points
- **Status:** ✅ **COMPLETED**
- **Sprint:** 2 - Authentication & User Management

## Description

Implement organization creation during the registration process and provide management capabilities.

## Technical Tasks

### ✅ Organization Creation Form

- [x] ✅ Create `components/onboarding/organization-form.tsx`:
  - Restaurant name input with validation
  - Restaurant description (optional)
  - Restaurant type selection (Fast food, Fine dining, etc.)
  - Contact information fields
  - Business address fields
  - Logo upload capability

### ✅ Slug Generation

- [x] ✅ Implement slug generation and validation logic:
  - Auto-generate slug from restaurant name
  - Check slug availability in real-time
  - Allow custom slug editing
  - Validate slug format (lowercase, hyphens, no spaces)
  - Reserve common slugs (admin, api, www, etc.)

### ✅ Onboarding Flow

- [x] ✅ Create onboarding pages:
  - `app/(auth)/onboarding/organization/page.tsx`
  - `app/(auth)/onboarding/complete/page.tsx`
  - `app/(auth)/onboarding/layout.tsx`

### ✅ Database Integration

- [x] ✅ Implement organization creation logic:
  - Create organization record in database
  - Set up automatic owner role assignment
  - Initialize default menu structure
  - Activate trial period (14 days)
  - Store organization ID in user session

### ✅ User Assignment

- [x] ✅ Set up user-organization relationship:
  - Link user to organization
  - Assign owner role automatically
  - Update user permissions
  - Create organization context

### ✅ Organization Selection

- [x] ✅ Add organization selection for existing users:
  - Organization switcher component
  - Multi-organization support for future
  - Current organization display in header

### ✅ Validation & Security

- [x] ✅ Implement comprehensive validation:
  - Organization name requirements
  - Slug uniqueness checking
  - Input sanitization
  - Rate limiting for creation attempts

## Acceptance Criteria

### ✅ Functional Requirements

- [x] ✅ Organization is created during registration flow
- [x] ✅ Unique slug validation works correctly
- [x] ✅ Owner role is automatically assigned
- [x] ✅ Trial period is activated by default (14 days)
- [x] ✅ User is redirected to dashboard after creation

### ✅ Data Requirements

- [x] ✅ Organization data is properly stored
- [x] ✅ User-organization relationship is established
- [x] ✅ Default menu structure is initialized
- [x] ✅ All required fields are validated

### ✅ UX Requirements

- [x] ✅ Smooth onboarding experience
- [x] ✅ Clear progress indicators
- [x] ✅ Helpful error messages
- [x] ✅ Mobile-responsive design

## Dependencies

- ✅ RS-002 (Database Schema Implementation) - **COMPLETED**
- ✅ RS-005 (Authentication Logic Implementation) - **COMPLETED**

## Database Operations

### Organization Creation

```typescript
interface OrganizationData {
  name: string;
  slug: string;
  description?: string;
  contact_email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country: string;
  postal_code?: string;
  subscription_status: "trial";
  trial_ends_at: Date; // 14 days from creation
}
```

### User Update

```typescript
interface UserUpdate {
  organization_id: string;
  role: "owner";
  permissions: {
    manage_organization: true;
    manage_users: true;
    manage_menu: true;
    manage_billing: true;
  };
}
```

## File Structure

```
app/
├── (auth)/
│   └── onboarding/
│       ├── layout.tsx                    ✅ CREATED
│       ├── organization/
│       │   └── page.tsx                  ✅ CREATED
│       └── complete/
│           └── page.tsx                  ✅ CREATED
├── api/
│   └── organizations/
│       ├── route.ts                      ✅ CREATED
│       └── check-slug/
│           └── route.ts                  ✅ CREATED
components/
├── onboarding/
│   └── organization-form.tsx             ✅ CREATED
├── ui/
│   └── select.tsx                        ✅ CREATED
lib/
├── services/
│   └── organization.ts                   ✅ CREATED
├── validations/
│   └── organization.ts                   ✅ CREATED
└── utils/
    └── slug.ts                           ✅ CREATED
```

## Validation Schema

```typescript
const organizationSchema = z.object({
  name: z
    .string()
    .min(2, "Restaurant name must be at least 2 characters")
    .max(100, "Restaurant name must be less than 100 characters"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(50, "Slug must be less than 50 characters")
    .regex(
      /^[a-z0-9-]+$/,
      "Slug can only contain lowercase letters, numbers, and hyphens"
    ),
  description: z.string().max(500).optional(),
  contact_email: z.string().email().optional(),
  phone: z.string().optional(),
  // ... address fields
});
```

## Slug Generation Logic

### Auto-generation Rules

1. Convert restaurant name to lowercase
2. Replace spaces with hyphens
3. Remove special characters
4. Ensure uniqueness with number suffix if needed

### Reserved Slugs

```typescript
const RESERVED_SLUGS = [
  "admin",
  "api",
  "www",
  "app",
  "dashboard",
  "auth",
  "login",
  "register",
  "help",
  "support",
  "billing",
  "pricing",
  "about",
  "contact",
];
```

## Onboarding Flow

### Step 1: Registration

1. User completes registration form
2. Email verification (if required)
3. User is redirected to organization setup

### Step 2: Organization Creation

1. Organization details form
2. Slug generation and validation
3. Terms of service acceptance

### Step 3: Completion

1. Organization created in database
2. User assigned as owner
3. Trial period activated
4. Redirect to dashboard

## Testing Requirements

- [ ] 📋 Unit tests for slug generation
- [ ] 📋 Integration tests for organization creation
- [ ] 📋 Validation testing for all form fields
- [ ] 📋 Slug uniqueness testing
- [ ] 📋 User role assignment testing

## Error Handling

- Duplicate slug handling
- Database constraint violations
- Network error recovery
- User-friendly error messages

## Success Metrics

- Organization creation completion rate
- Time to complete onboarding
- Slug generation success rate
- User satisfaction with flow

## Related Stories

- Story 2.2: Organization Creation
- Story 2.3: Multi-tenant Middleware (uses organizations)

## Next Steps After Completion

1. Implement multi-tenant middleware (Sprint 2)
2. Create organization management interface
3. Add team invitation system
4. Set up billing integration

## Notes

- Consider organization templates for different restaurant types
- Plan for future multi-organization support
- Ensure slug changes are handled gracefully
- Consider SEO implications of slug choices
- Add analytics tracking for onboarding funnel

## Implementation Summary

### ✅ Completed Features

1. **Organization Form Component** - Complete form with all required fields, real-time slug validation, and restaurant type selection
2. **Slug Generation & Validation** - Auto-generation from restaurant name, real-time availability checking, and suggestion system
3. **API Routes** - Organization creation and slug checking endpoints with proper validation
4. **Onboarding Pages** - Complete flow with progress indicators and success page
5. **Database Integration** - Organization creation, user assignment, and default menu initialization
6. **Auth Flow Integration** - Updated callback and dashboard to handle organization creation flow
7. **Middleware Protection** - Added onboarding routes to protected routes
8. **UI Components** - Created Select component for restaurant type selection

### ✅ Key Technical Achievements

- **Real-time Slug Validation**: Debounced API calls for instant feedback
- **Automatic Role Assignment**: Users become owners of their organizations
- **Default Menu Structure**: Pre-populated menu categories for new restaurants
- **14-day Trial Period**: Automatic trial activation
- **Comprehensive Validation**: Client and server-side validation with Zod schemas
- **Error Handling**: User-friendly error messages and fallback suggestions
- **Mobile Responsive**: Fully responsive design with Tailwind CSS
- **Progress Indicators**: Clear visual feedback throughout the onboarding process
