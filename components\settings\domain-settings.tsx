"use client"
/* eslint-disable react/no-unescaped-entities */

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Globe, ExternalLink, CheckCircle, AlertCircle, Copy, RefreshCw } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"

const domainSchema = z.object({
  customDomain: z
    .string()
    .min(1, "Domain is required")
    .regex(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/,
      "Please enter a valid domain (e.g., menu.restaurant.com)"
    ),
})

type DomainFormData = z.infer<typeof domainSchema>

interface DomainSettingsProps {
  organization: {
    id: string
    name: string
    slug: string
    custom_domain: string | null
  }
}

export function DomainSettings({ organization }: DomainSettingsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [domainStatus, setDomainStatus] = useState<'pending' | 'verified' | 'failed' | null>(null)
  const { toast } = useToast()

  const form = useForm<DomainFormData>({
    resolver: zodResolver(domainSchema),
    defaultValues: {
      customDomain: organization.custom_domain || "",
    },
  })

  const currentSubdomain = `${organization.slug}.bezorg.nl`
  const hasCustomDomain = !!organization.custom_domain

  const onSubmit = async (data: DomainFormData) => {
    setIsLoading(true)
    
    try {
      const response = await fetch(`/api/organizations/${organization.id}/domain`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customDomain: data.customDomain,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update domain')
      }

      setDomainStatus('pending')
      toast({
        title: "Domain Updated",
        description: "Your custom domain has been saved. Please configure DNS settings.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update domain",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const verifyDomain = async () => {
    if (!organization.custom_domain) return
    
    setIsVerifying(true)
    
    try {
      const response = await fetch(`/api/organizations/${organization.id}/domain/verify`, {
        method: 'POST',
      })

      const result = await response.json()
      
      if (result.verified) {
        setDomainStatus('verified')
        toast({
          title: "Domain Verified",
          description: "Your custom domain is now active!",
        })
      } else {
        setDomainStatus('failed')
        toast({
          title: "Verification Failed",
          description: result.message || "Please check your DNS settings and try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify domain",
        variant: "destructive",
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "DNS record copied to clipboard",
    })
  }

  return (
    <div className="space-y-6">
      {/* Current Domain Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Domain Settings
          </CardTitle>
          <CardDescription>
            Configure your restaurant&apos;s custom domain for a professional appearance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Subdomain */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Current Subdomain</p>
              <p className="text-sm text-gray-600">{currentSubdomain}</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Active</Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`https://${currentSubdomain}`, '_blank')}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Custom Domain */}
          {hasCustomDomain && (
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="font-medium">Custom Domain</p>
                <p className="text-sm text-gray-600">{organization.custom_domain}</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={domainStatus === 'verified' ? 'default' : 'secondary'}
                >
                  {domainStatus === 'verified' ? 'Verified' : 'Pending'}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={verifyDomain}
                  disabled={isVerifying}
                >
                  {isVerifying ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Domain Configuration Form */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Domain Setup</CardTitle>
          <CardDescription>
            Add your own domain to make your restaurant menu more professional
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="customDomain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Domain</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="menu.restaurant.com"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter your custom domain (e.g., menu.restaurant.com or www.restaurant.nl)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Domain"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* DNS Configuration Instructions */}
      {form.watch('customDomain') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-orange-500" />
              DNS Configuration Required
            </CardTitle>
            <CardDescription>
              Add these DNS records to your domain provider to activate your custom domain
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium">CNAME Record</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard('bezorg.netlify.app')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Name</p>
                    <p className="font-mono">@</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Type</p>
                    <p className="font-mono">CNAME</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Value</p>
                    <p className="font-mono">bezorg.netlify.app</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium">WWW CNAME Record (Optional)</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard('bezorg.netlify.app')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Name</p>
                    <p className="font-mono">www</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Type</p>
                    <p className="font-mono">CNAME</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Value</p>
                    <p className="font-mono">bezorg.netlify.app</p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <h4 className="font-medium">Instructions:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>Log in to your domain provider (GoDaddy, Namecheap, etc.)</li>
                <li>Go to DNS management or DNS settings</li>
                <li>Add the CNAME records shown above</li>
                <li>Wait 5-30 minutes for DNS propagation</li>
                <li>Click "Verify Domain" to check if it's working</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
