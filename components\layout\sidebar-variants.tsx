'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useSidebar, navigationItems, navigationGroups } from './sidebar-navigation'
import {
  ChevronLeft,
  ChevronRight,
  X,
  LogOut,
  Search,
  Bell,
  Plus,

} from 'lucide-react'

interface AdvancedSidebarProps {
  variant: 'neon' | 'floating' | 'compact' | 'premium' | 'animated'
  className?: string
}

export function AdvancedSidebar({ variant, className }: AdvancedSidebarProps) {
  const { isCollapsed, setIsCollapsed, isMobileOpen, setIsMobileOpen } = useSidebar()
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = useState('')

  const sidebarVariants = {
    neon: 'bg-black border-r border-cyan-500/30 shadow-[0_0_20px_rgba(6,182,212,0.3)]',
    floating: 'bg-white/95 backdrop-blur-xl border-r border-gray-200/50 shadow-2xl rounded-r-2xl m-2 h-[calc(100vh-1rem)]',
    compact: 'bg-slate-800 border-r border-slate-700',
    premium: 'bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 border-r border-slate-700/50',
    animated: 'bg-white border-r border-gray-200 shadow-lg'
  }

  const textVariants = {
    neon: 'text-cyan-100',
    floating: 'text-gray-900',
    compact: 'text-gray-100',
    premium: 'text-gray-100',
    animated: 'text-gray-900'
  }

  const hoverVariants = {
    neon: 'hover:bg-cyan-500/20 hover:shadow-[0_0_10px_rgba(6,182,212,0.5)]',
    floating: 'hover:bg-gray-100/80',
    compact: 'hover:bg-slate-700',
    premium: 'hover:bg-slate-700/50',
    animated: 'hover:bg-orange-50 hover:scale-105'
  }

  const activeVariants = {
    neon: 'bg-cyan-500/30 text-cyan-300 border-r-2 border-cyan-400 shadow-[0_0_15px_rgba(6,182,212,0.6)]',
    floating: 'bg-orange-100 text-orange-700 border-r-4 border-orange-500 shadow-lg',
    compact: 'bg-slate-700 text-orange-400 border-r-2 border-orange-400',
    premium: 'bg-gradient-to-r from-orange-500/20 to-red-500/20 text-orange-300 border-r-2 border-orange-400',
    animated: 'bg-orange-100 text-orange-700 border-r-4 border-orange-500 transform scale-105'
  }

  const accentColors = {
    neon: 'text-cyan-400',
    floating: 'text-orange-600',
    compact: 'text-orange-400',
    premium: 'text-orange-400',
    animated: 'text-orange-600'
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed left-0 top-0 z-50 transition-all duration-300 ease-in-out lg:relative lg:z-auto',
          variant === 'floating' ? 'h-full' : 'h-full',
          isCollapsed ? 'w-16' : 'w-64',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
          sidebarVariants[variant],
          className
        )}
      >
        {/* Header */}
        <div className={cn(
          'flex h-16 items-center justify-between px-4',
          variant === 'neon' ? 'border-b border-cyan-500/30' : 'border-b border-current/10'
        )}>
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div className={cn(
                'h-8 w-8 rounded-lg flex items-center justify-center',
                variant === 'neon' ? 'bg-cyan-500/30 shadow-[0_0_10px_rgba(6,182,212,0.5)]' :
                variant === 'floating' ? 'bg-gradient-to-r from-orange-500 to-red-500 shadow-lg' :
                variant === 'premium' ? 'bg-gradient-to-r from-orange-500 to-red-500' :
                'bg-gradient-to-r from-orange-500 to-red-500'
              )}>
                <span className={cn(
                  'font-bold text-sm',
                  variant === 'neon' ? 'text-cyan-300' : 'text-white'
                )}>R</span>
              </div>
              <span className={cn(
                'font-bold text-lg truncate',
                textVariants[variant],
                variant === 'neon' && 'text-shadow-[0_0_10px_rgba(6,182,212,0.8)]'
              )}>
                RestaurantSaaS
              </span>
            </div>
          )}
          
          {/* Toggle buttons */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={cn(
                'hidden lg:flex h-8 w-8 p-0',
                variant === 'neon' ? 'hover:bg-cyan-500/20 text-cyan-300' :
                variant === 'premium' ? 'hover:bg-slate-700/50 text-gray-300' :
                ''
              )}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                'lg:hidden h-8 w-8 p-0',
                variant === 'neon' ? 'hover:bg-cyan-500/20 text-cyan-300' :
                variant === 'premium' ? 'hover:bg-slate-700/50 text-gray-300' :
                ''
              )}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search Bar (for some variants) */}
        {!isCollapsed && (variant === 'premium' || variant === 'floating') && (
          <div className="p-4">
            <div className="relative">
              <Search className={cn('absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4', accentColors[variant])} />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  'w-full pl-10 pr-4 py-2 rounded-lg text-sm transition-all',
                  variant === 'premium' ? 'bg-slate-700/50 border border-slate-600 text-gray-100 placeholder-gray-400 focus:border-orange-400' :
                  'bg-gray-100 border border-gray-200 text-gray-900 placeholder-gray-500 focus:border-orange-400'
                )}
              />
            </div>
          </div>
        )}

        {/* Quick Actions (for premium variant) */}
        {!isCollapsed && variant === 'premium' && (
          <div className="px-4 pb-4">
            <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg">
              <Plus className="h-4 w-4 mr-2" />
              Quick Add
            </Button>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-6">
          {Object.entries(navigationGroups).map(([groupKey, group]) => {
            const groupItems = navigationItems.filter(item => 
              item.group === groupKey && 
              (searchQuery === '' || item.title.toLowerCase().includes(searchQuery.toLowerCase()))
            )
            
            if (groupItems.length === 0) return null
            
            return (
              <div key={groupKey}>
                {!isCollapsed && (
                  <div className="flex items-center space-x-2 mb-3">
                    <group.icon className={cn('h-4 w-4', accentColors[variant])} />
                    <span className={cn(
                      'text-xs font-semibold uppercase tracking-wider',
                      textVariants[variant],
                      'opacity-70'
                    )}>
                      {group.title}
                    </span>
                  </div>
                )}
                
                <div className="space-y-1">
                  {groupItems.map((item) => {
                    const isActive = pathname === item.href
                    const Icon = item.icon
                    
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                          'flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200',
                          variant === 'animated' && 'transform transition-transform',
                          isActive ? activeVariants[variant] : cn(hoverVariants[variant], textVariants[variant]),
                          isCollapsed && 'justify-center px-2'
                        )}
                      >
                        <Icon className={cn(
                          'h-5 w-5 flex-shrink-0',
                          variant === 'neon' && isActive && 'drop-shadow-[0_0_5px_rgba(6,182,212,0.8)]'
                        )} />
                        {!isCollapsed && (
                          <>
                            <span className="flex-1 truncate">{item.title}</span>
                            {item.badge && (
                              <Badge 
                                variant={isActive ? "default" : "secondary"}
                                className={cn(
                                  'h-5 text-xs',
                                  variant === 'neon' && 'bg-cyan-500/30 text-cyan-300 border-cyan-400/50'
                                )}
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        )}
                      </Link>
                    )
                  })}
                </div>
                
                {!isCollapsed && groupKey !== 'management' && (
                  <Separator className={cn(
                    'my-4',
                    variant === 'neon' ? 'bg-cyan-500/30' : 'opacity-30'
                  )} />
                )}
              </div>
            )
          })}
        </nav>

        {/* Notifications (for some variants) */}
        {!isCollapsed && (variant === 'premium' || variant === 'floating') && (
          <div className="px-4 py-2">
            <div className={cn(
              'flex items-center space-x-2 p-2 rounded-lg',
              variant === 'premium' ? 'bg-slate-700/30' : 'bg-orange-50'
            )}>
              <Bell className={cn('h-4 w-4', accentColors[variant])} />
              <span className={cn('text-xs', textVariants[variant])}>
                3 new notifications
              </span>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className={cn(
          'p-4',
          variant === 'neon' ? 'border-t border-cyan-500/30' : 'border-t border-current/10'
        )}>
          <div className="flex items-center space-x-3">
            {!isCollapsed && (
              <>
                <div className={cn(
                  'h-8 w-8 rounded-full flex items-center justify-center',
                  variant === 'neon' ? 'bg-cyan-500/30 shadow-[0_0_10px_rgba(6,182,212,0.5)]' :
                  'bg-gradient-to-r from-orange-400 to-red-400'
                )}>
                  <span className={cn(
                    'text-sm font-medium',
                    variant === 'neon' ? 'text-cyan-300' : 'text-white'
                  )}>JD</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className={cn('text-sm font-medium truncate', textVariants[variant])}>
                    John Doe
                  </p>
                  <p className={cn('text-xs truncate', textVariants[variant], 'opacity-70')}>
                    Restaurant Owner
                  </p>
                </div>
              </>
            )}
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'h-8 w-8 p-0',
                variant === 'neon' ? 'hover:bg-cyan-500/20 text-cyan-300' :
                variant === 'premium' ? 'hover:bg-slate-700/50 text-gray-300' :
                '',
                isCollapsed && 'mx-auto'
              )}
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
