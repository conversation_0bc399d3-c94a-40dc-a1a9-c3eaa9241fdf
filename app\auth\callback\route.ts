import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createUserProfile } from '@/lib/auth'
import { canUserCreateOrganization } from '@/lib/services/organization'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/dashboard'
  const type = searchParams.get('type')

  if (code) {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.delete({ name, ...options })
          },
        },
      }
    )

    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error && data.user) {
      // Ensure user profile exists in database for new users
      try {
        await createUserProfile(data.user)
      } catch (profileError) {
        console.error('Error creating user profile in callback:', profileError)
        // Continue with auth flow even if profile creation fails
      }

      // Check if this is a password recovery
      if (type === 'recovery') {
        return NextResponse.redirect(`${origin}/update-password`)
      }
      
      // Check if this is a signup confirmation
      if (type === 'signup') {
        // Check if user needs to create an organization
        try {
          const canCreate = await canUserCreateOrganization(data.user.id)
          if (canCreate) {
            // Redirect to organization setup for new users
            return NextResponse.redirect(`${origin}/onboarding/organization`)
          }
        } catch (error) {
          console.error('Error checking organization status:', error)
          // If check fails, continue to dashboard
        }
        
        // User already has an organization, go to dashboard
        return NextResponse.redirect(`${origin}/dashboard?welcome=true`)
      }
      
      // For successful login/OAuth, check if user needs organization setup
      try {
        const canCreate = await canUserCreateOrganization(data.user.id)
        if (canCreate) {
          // User doesn't have an organization, redirect to setup
          return NextResponse.redirect(`${origin}/onboarding/organization`)
        }
      } catch (error) {
        console.error('Error checking organization status:', error)
        // If check fails, continue to intended destination
      }
      
      // User has organization, redirect to intended destination
      return NextResponse.redirect(`${origin}${next}`)
    }
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
} 