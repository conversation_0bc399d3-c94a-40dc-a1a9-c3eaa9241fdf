import { z } from 'zod';
import { PERMISSIONS } from '@/types/roles';

// Permission schema
export const permissionSchema = z.object({
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  scope: z.enum(['own', 'team', 'organization']).optional(),
});

// Role schemas
export const createRoleSchema = z.object({
  name: z
    .string()
    .min(1, 'Role name is required')
    .max(50, 'Role name must be 50 characters or less')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores'),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  permissions: z
    .array(permissionSchema)
    .min(1, 'At least one permission is required'),
});

export const updateRoleSchema = z.object({
  name: z
    .string()
    .min(1, 'Role name is required')
    .max(50, 'Role name must be 50 characters or less')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores')
    .optional(),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  permissions: z
    .array(permissionSchema)
    .min(1, 'At least one permission is required')
    .optional(),
});

export const roleIdSchema = z.object({
  roleId: z.string().uuid('Invalid role ID'),
});

// User role assignment schemas
export const assignRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

export const removeRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
});

export const userIdSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
});

// Bulk operations schemas
export const bulkAssignRolesSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1, 'At least one user is required'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

export const bulkRemoveRolesSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1, 'At least one user is required'),
  roleId: z.string().uuid('Invalid role ID'),
});

// Permission checking schema
export const hasPermissionSchema = z.object({
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  scope: z.enum(['own', 'team', 'organization']).optional(),
});

// Predefined permission validation
const validPermissions = Object.values(PERMISSIONS);
export const validPermissionSchema = z.object({
  resource: z.string().refine(
    (val) => validPermissions.some(p => p.resource === val),
    'Invalid permission resource'
  ),
  action: z.string().refine(
    (val) => validPermissions.some(p => p.action === val),
    'Invalid permission action'
  ),
  scope: z.enum(['own', 'team', 'organization']).optional(),
});

// Export types
export type CreateRoleInput = z.infer<typeof createRoleSchema>;
export type UpdateRoleInput = z.infer<typeof updateRoleSchema>;
export type AssignRoleInput = z.infer<typeof assignRoleSchema>;
export type RemoveRoleInput = z.infer<typeof removeRoleSchema>;
export type BulkAssignRolesInput = z.infer<typeof bulkAssignRolesSchema>;
export type BulkRemoveRolesInput = z.infer<typeof bulkRemoveRolesSchema>;
export type HasPermissionInput = z.infer<typeof hasPermissionSchema>; 