import { Badge } from '@/components/ui/badge';
import { Permission } from '@/types/roles';
import { cn } from '@/lib/utils';

interface PermissionBadgeProps {
  permission: Permission;
  variant?: 'default' | 'secondary' | 'outline';
  className?: string;
}

export function PermissionBadge({ 
  permission, 
  variant = 'outline', 
  className 
}: PermissionBadgeProps) {
  const getPermissionColor = (resource: string) => {
    // Color coding based on resource type
    const colorMap: Record<string, string> = {
      menu: 'bg-blue-50 text-blue-700 border-blue-200',
      user: 'bg-green-50 text-green-700 border-green-200',
      role: 'bg-purple-50 text-purple-700 border-purple-200',
      organization: 'bg-orange-50 text-orange-700 border-orange-200',
      analytics: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      settings: 'bg-gray-50 text-gray-700 border-gray-200',
    };

    return colorMap[resource] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  const formatPermissionText = (permission: Permission) => {
    const actionMap: Record<string, string> = {
      create: 'Create',
      read: 'View',
      update: 'Edit',
      delete: 'Delete',
      manage: 'Manage',
      assign: 'Assign',
      invite: 'Invite',
      remove: 'Remove',
      publish: 'Publish',
      export: 'Export',
      view: 'View',
      settings: 'Settings',
      billing: 'Billing',
    };

    const resourceMap: Record<string, string> = {
      menu: 'Menu',
      user: 'User',
      role: 'Role',
      organization: 'Org',
      analytics: 'Analytics',
      settings: 'Settings',
    };

    const action = actionMap[permission.action] || permission.action;
    const resource = resourceMap[permission.resource] || permission.resource;

    return `${action} ${resource}`;
  };

  const permissionText = formatPermissionText(permission);
  const colorClass = variant === 'outline' ? getPermissionColor(permission.resource) : '';

  return (
    <Badge 
      variant={variant}
      className={cn(
        'text-xs font-medium',
        variant === 'outline' && colorClass,
        className
      )}
      title={`${permission.resource}:${permission.action}${permission.scope ? ` (${permission.scope})` : ''}`}
    >
      {permissionText}
      {permission.scope && (
        <span className="ml-1 opacity-60">
          ({permission.scope})
        </span>
      )}
    </Badge>
  );
} 