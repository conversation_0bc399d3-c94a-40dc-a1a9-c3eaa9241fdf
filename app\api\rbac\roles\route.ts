import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { createRole, getRoles } from '@/lib/db/rbac-queries';
import { createRoleSchema } from '@/lib/validations/rbac';
import { requirePermission } from '@/lib/auth/rbac';
import { PERMISSIONS } from '@/types/roles';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Check permission to read roles
    await requirePermission(PERMISSIONS.ROLE_READ, organizationId);

    const includeSystemOnly = searchParams.get('systemOnly') === 'true';
    const roles = await getRoles(organizationId, includeSystemOnly);

    return NextResponse.json({ roles });
  } catch (error: any) {
    console.error('Error fetching roles:', error);
    
    if (error.name === 'PermissionError') {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { organizationId, ...roleData } = body;

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Check permission to create roles
    await requirePermission(PERMISSIONS.ROLE_CREATE, organizationId);

    // Validate role data
    const result = createRoleSchema.safeParse(roleData);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid role data', details: result.error.errors },
        { status: 400 }
      );
    }

    const role = await createRole(organizationId, result.data, user.id);

    return NextResponse.json({ role }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating role:', error);
    
    if (error.name === 'PermissionError') {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    
    if (error.code === 'DUPLICATE_NAME') {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 