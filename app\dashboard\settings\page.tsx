import { Suspense } from 'react';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { SettingsPageClient } from '@/components/settings/settings-page-client';
import { getUserSettings } from '@/lib/db/settings-queries';

export default async function SettingsPage() {
  const supabase = createClient();
  
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error || !session) {
    redirect('/auth/login');
  }

  // Get user data
  const { data: userData } = await supabase
    .from('users')
    .select('*')
    .eq('id', session.user.id)
    .single();

  if (!userData || !userData.organization_id) {
    redirect('/onboarding');
  }

  // Get organization data
  const { data: organizationData } = await supabase
    .from('organizations')
    .select('*')
    .eq('id', userData.organization_id)
    .single();

  if (!organizationData) {
    redirect('/onboarding');
  }

  // Get initial settings
  const initialSettings = await getUserSettings(session.user.id, userData.organization_id);

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>
      
      <Suspense fallback={<div>Loading settings...</div>}>
        <SettingsPageClient
          organizationId={userData.organization_id}
          userId={session.user.id}
          userRole={userData.role}
          initialSettings={initialSettings}
          userEmail={session.user.email || ''}
          userName={userData.full_name || ''}
          organizationName={organizationData.name}
        />
      </Suspense>
    </div>
  );
} 