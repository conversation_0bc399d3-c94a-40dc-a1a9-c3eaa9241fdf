# MCP Server Setup Guide - Restaurant SaaS Platform

## 🎯 Overview

This guide helps AI coding agents set up the optimal MCP (Model Context Protocol) environment for developing the restaurant SaaS platform efficiently and securely.

---

## 🔧 Core MCP Servers (MANDATORY)

### **1. Supabase MCP (Database Operations)**

```json
{
  "mcpServers": {
    "supabase": {
      "command": "mcp-supabase",
      "args": [],
      "env": {
        "SUPABASE_URL": "your_supabase_project_url",
        "SUPABASE_SERVICE_ROLE_KEY": "your_service_role_key"
      }
    }
  }
}
```

**Usage Patterns:**

- Database schema management
- Migration tracking
- Query execution and optimization
- RLS policy management

### **2. File System MCP (Code Management)**

Built-in tools that should be used:

- `read_file` - Reading source files
- `edit_file` - Making code changes
- `list_dir` - Exploring project structure
- `file_search` - Finding files by name

### **3. Search MCPs (Code Discovery)**

Built-in tools for navigation:

- `codebase_search` - Semantic code search
- `grep_search` - Exact pattern matching

---

## 🚀 Recommended MCP Servers

### **GitHub MCP (Repository Management)**

```json
{
  "mcpServers": {
    "github": {
      "command": "mcp-github",
      "args": [],
      "env": {
        "GITHUB_TOKEN": "your_github_token"
      }
    }
  }
}
```

**Features:**

- Repository management
- Pull request creation
- Issue tracking
- Branch management

### **Stripe MCP (Payment Integration)**

```json
{
  "mcpServers": {
    "stripe": {
      "command": "mcp-stripe",
      "args": [],
      "env": {
        "STRIPE_SECRET_KEY": "sk_test_...",
        "STRIPE_WEBHOOK_SECRET": "whsec_..."
      }
    }
  }
}
```

**Features:**

- Subscription management
- Payment testing
- Webhook validation
- Customer management

### **Docker MCP (Containerization)**

```json
{
  "mcpServers": {
    "docker": {
      "command": "mcp-docker",
      "args": []
    }
  }
}
```

**Features:**

- Container management
- Development environment setup
- Production deployment preparation

---

## 📊 Development Enhancement MCPs

### **Context7 MCP (Documentation)**

```json
{
  "mcpServers": {
    "context7": {
      "command": "mcp-context7",
      "args": []
    }
  }
}
```

**Features:**

- Library documentation lookup
- API reference integration
- Code examples and snippets

### **Browser MCP (Testing & Debugging)**

```json
{
  "mcpServers": {
    "browser": {
      "command": "mcp-browser",
      "args": []
    }
  }
}
```

**Features:**

- Automated testing
- Performance auditing
- SEO optimization
- Debugging assistance

### **Brave Search MCP (Research)**

```json
{
  "mcpServers": {
    "brave-search": {
      "command": "mcp-brave-search",
      "args": [],
      "env": {
        "BRAVE_API_KEY": "your_brave_api_key"
      }
    }
  }
}
```

**Features:**

- Technology research
- Best practices lookup
- Problem-solving assistance
- Documentation discovery

---

## 🏗️ Project-Specific MCP Usage

### **Database Development Workflow**

#### 1. Schema Exploration

```typescript
// Use mcp_supabase-mcp_get_schemas
// Use mcp_supabase-mcp_get_tables
// Use mcp_supabase-mcp_get_table_schema
```

#### 2. Migration Management

```typescript
// Enable unsafe mode for schema changes
// Use mcp_supabase-mcp_live_dangerously
// Execute migrations with mcp_supabase-mcp_execute_postgresql
// Track with mcp_supabase-mcp_retrieve_migrations
```

#### 3. Data Operations

```typescript
// Read operations in safe mode
// Write operations require unsafe mode
// Always test queries before production
```

### **Code Development Workflow**

#### 1. Feature Discovery

```typescript
// Search existing implementations
// Use codebase_search for similar features
// Use grep_search for specific functions
```

#### 2. File Management

```typescript
// Read relevant files with read_file
// Make parallel reads for efficiency
// Edit files with clear instructions
```

#### 3. Code Quality

```typescript
// Follow TypeScript strict mode
// Implement proper error handling
// Add comprehensive tests
```

### **Payment Integration Workflow**

#### 1. Stripe Setup

```typescript
// Use Stripe MCP for testing
// Validate webhook endpoints
// Test subscription flows
```

#### 2. Security Validation

```typescript
// Never expose secrets
// Always validate webhook signatures
// Implement proper error handling
```

---

## 🔐 Security Best Practices

### **Environment Variables Management**

```bash
# Development
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Production (Never commit these)
SUPABASE_SERVICE_ROLE_KEY=your_production_key
STRIPE_SECRET_KEY=sk_live_your_live_key
```

### **MCP Security Rules**

1. **Never commit MCP configurations with secrets**
2. **Use environment variables for all sensitive data**
3. **Enable safe mode by default**
4. **Only enable unsafe mode when necessary**
5. **Validate all inputs from external MCPs**

---

## 📋 MCP Usage Patterns for Restaurant SaaS

### **Multi-tenancy Development**

```typescript
// Pattern: Always verify restaurant ownership
export async function getRestaurantData(restaurantId: string, userId: string) {
  // Use Supabase MCP with RLS policies
  const { data } = await supabase
    .from("restaurants")
    .select("*")
    .eq("id", restaurantId)
    .eq("user_id", userId)
    .single();

  return data;
}
```

### **Subscription Management**

```typescript
// Pattern: Sync Stripe with database
export async function handleSubscriptionUpdate(event: Stripe.Event) {
  // Use Stripe MCP for webhook validation
  // Use Supabase MCP for data updates

  switch (event.type) {
    case "customer.subscription.updated":
      await updateSubscriptionStatus(event.data.object);
      break;
  }
}
```

### **Domain Management**

```typescript
// Pattern: Validate domain routing
export async function setupCustomDomain(domain: string, restaurantId: string) {
  // Use DNS MCP for domain validation
  // Use Supabase MCP for restaurant association

  const isValid = await validateDomain(domain);
  if (isValid) {
    await associateDomainWithRestaurant(domain, restaurantId);
  }
}
```

---

## 🧪 Testing with MCPs

### **Database Testing**

```typescript
// Use Supabase MCP in test mode
beforeEach(async () => {
  await setupTestDatabase();
  await seedTestData();
});

afterEach(async () => {
  await cleanupTestDatabase();
});
```

### **Payment Testing**

```typescript
// Use Stripe MCP test mode
const testCustomer = await stripe.customers.create({
  email: "<EMAIL>",
  payment_method: "pm_card_visa", // Test card
});
```

### **Integration Testing**

```typescript
// Use Browser MCP for E2E tests
await browser.goto("/restaurant/test-restaurant");
await browser.fill('[data-testid="menu-item-name"]', "Test Item");
await browser.click('[data-testid="save-menu-item"]');
```

---

## 📊 Performance Monitoring with MCPs

### **Database Performance**

```typescript
// Monitor query performance
const startTime = performance.now();
const result = await supabase.from("restaurants").select("*");
const duration = performance.now() - startTime;

// Log slow queries (>100ms)
if (duration > 100) {
  console.warn(`Slow query detected: ${duration}ms`);
}
```

### **API Performance**

```typescript
// Use Browser MCP for performance auditing
const auditResult = await browser.runAudit();
console.log("Core Web Vitals:", auditResult.performance);
```

---

## 🔄 Development Workflow Integration

### **Daily Development Process**

1. **Morning Setup**

   ```bash
   # Check MCP connections
   # Verify database connectivity
   # Update dependencies
   ```

2. **Feature Development**

   ```bash
   # Search existing code (codebase_search)
   # Read relevant files (read_file)
   # Implement changes (edit_file)
   # Test with database (supabase-mcp)
   ```

3. **Testing & Validation**

   ```bash
   # Run unit tests
   # Test payment flows (stripe-mcp)
   # Validate UI (browser-mcp)
   ```

4. **Documentation**
   ```bash
   # Update documentation
   # Generate API docs
   # Update README files
   ```

### **Weekly Review Process**

1. **Performance Review**

   - Database query optimization
   - Bundle size analysis
   - Performance metrics review

2. **Security Review**

   - Dependency updates
   - Security scan results
   - Access control validation

3. **Code Quality Review**
   - Test coverage analysis
   - Code complexity metrics
   - Documentation completeness

---

## 🎯 MCP Success Metrics

### **Development Efficiency**

- Reduced development time through MCP automation
- Faster code discovery and navigation
- Improved code quality through automated checks

### **Quality Metrics**

- 100% test coverage for critical paths
- Zero security vulnerabilities
- Sub-100ms database query performance
- 95+ Lighthouse scores

### **Collaboration Metrics**

- Consistent code patterns across team
- Reduced onboarding time for new developers
- Improved documentation quality

---

## 🚨 Troubleshooting Guide

### **Common MCP Issues**

#### Database Connection Issues

```bash
# Check Supabase MCP configuration
# Verify environment variables
# Test connection manually
```

#### Payment Integration Issues

```bash
# Validate Stripe webhook secrets
# Check API key permissions
# Test in Stripe dashboard
```

#### Build/Deployment Issues

```bash
# Verify Netlify configuration
# Check environment variables
# Test build locally
```

### **Debug Commands**

```bash
# Test MCP connections
mcp list-servers

# Validate environment
env | grep -E "(SUPABASE|STRIPE|NETLIFY)"

# Test database connectivity
curl -X GET "https://your-project.supabase.co/rest/v1/restaurants" \
  -H "apikey: your-anon-key"
```

---

## 📚 Additional Resources

### **MCP Documentation**

- [Model Context Protocol Specification](https://github.com/modelcontextprotocol/specification)
- [MCP Server Registry](https://github.com/modelcontextprotocol/servers)

### **Project Resources**

- `AI_CODING_RULES.md` - Comprehensive coding guidelines
- `docs/USER_STORIES.md` - Feature requirements
- `docs/TICKETS.md` - Development tasks
- `docs/NETLIFY_DEPLOYMENT.md` - Deployment guide

### **Technology Documentation**

- [Next.js App Router](https://nextjs.org/docs/app)
- [Supabase Documentation](https://supabase.com/docs)
- [Stripe API Reference](https://stripe.com/docs/api)

---

**Remember**: MCPs are tools to enhance development efficiency while maintaining code quality and security. Always follow the security guidelines and validate all operations before production deployment.
