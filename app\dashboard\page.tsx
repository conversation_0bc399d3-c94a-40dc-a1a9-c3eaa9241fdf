import { requireAuth } from '@/lib/auth'
import { canUserCreateOrganization } from '@/lib/services/organization'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  // Check if user is authenticated first
  const user = await requireAuth()
  
  // Check if user needs to create an organization
  try {
    const needsOrganization = await canUserCreateOrganization(user.id)
    if (needsOrganization) {
      // User doesn't have an organization, redirect to setup
      redirect('/onboarding/organization')
    }
  } catch (error) {
    console.error('Error checking organization status:', error)
    // If check fails, continue to dashboard
  }
  
  // User has organization, redirect to overview page
  redirect('/dashboard/overview')
} 