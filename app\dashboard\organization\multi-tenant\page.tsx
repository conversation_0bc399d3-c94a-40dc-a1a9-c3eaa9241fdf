import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Building2, Globe, Users, Settings, Shield, Key } from 'lucide-react';
import Link from 'next/link';

export default async function MultiTenantManagementPage() {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value;
        }
      }
    }
  );

  const { data: { user } } = await supabase.auth.getUser();
  const session = user ? { user } : null;
  
  if (!session) {
    redirect('/login');
  }

  // Get current user's organization
  const { data: userProfile } = await supabase
    .from('users')
    .select(`
      *,
      organizations (*)
    `)
    .eq('id', session.user.id)
    .single();

  if (!userProfile?.organizations) {
    redirect('/onboarding/organization');
  }

  const organization = userProfile.organizations;

  // Check if user has admin/owner permissions
  const canManageTenant = ['owner', 'admin'].includes(userProfile.role);

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Multi-Tenant Settings</h1>
          <p className="text-gray-600 mt-2">
            Manage your organization&apos;s multi-tenant configuration and access
          </p>
        </div>
        <Link href="/test-tenant">
          <Button variant="outline">
            Test Configuration
          </Button>
        </Link>
      </div>

      {/* Organization Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Information
          </CardTitle>
          <CardDescription>
            Basic information about your organization and tenant configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="org-name">Organization Name</Label>
              <Input id="org-name" value={organization.name} disabled />
            </div>
            <div>
              <Label htmlFor="org-slug">Tenant Slug</Label>
              <Input id="org-slug" value={organization.slug} disabled />
              <p className="text-xs text-gray-500 mt-1">
                Used for subdomain: {organization.slug}.yourdomain.com
              </p>
            </div>
            <div>
              <Label htmlFor="org-id">Organization ID</Label>
              <Input id="org-id" value={organization.id} disabled className="font-mono text-xs" />
            </div>
            <div>
              <Label>Subscription Status</Label>
              <div className="mt-1">
                <Badge variant={
                  organization.subscription_status === 'active' ? 'default' :
                  organization.subscription_status === 'trial' ? 'secondary' : 'destructive'
                }>
                  {organization.subscription_status}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Domain Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Domain Configuration
          </CardTitle>
          <CardDescription>
            Configure how customers access your restaurant
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Subdomain Access</h4>
              <div className="bg-gray-50 p-3 rounded border">
                <p className="text-sm text-gray-700">
                  <strong>Current subdomain:</strong> {organization.slug}.yourdomain.com
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Customers can access your restaurant at this subdomain
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-2">Custom Domain (Pro Feature)</h4>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="custom-domain">Custom Domain</Label>
                  <Input 
                    id="custom-domain" 
                    placeholder="www.yourrestaurant.com" 
                    disabled={!canManageTenant}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Point your domain to our servers to use your own domain
                  </p>
                </div>
                {canManageTenant && (
                  <div className="flex gap-2">
                    <Button disabled>
                      Save Custom Domain
                    </Button>
                    <Button variant="outline" disabled>
                      Verify DNS
                    </Button>
                  </div>
                )}
                <div className="bg-blue-50 p-3 rounded">
                  <p className="text-sm text-blue-800">
                    💡 Custom domains require a Pro or Enterprise subscription
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Access Control & Security
          </CardTitle>
          <CardDescription>
            Manage user access and security settings for your organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Your Role</h4>
              <Badge variant="outline" className="text-sm">
                {userProfile.role}
              </Badge>
              <p className="text-xs text-gray-500 mt-1">
                Your permission level in this organization
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Multi-Tenant Status</h4>
              <Badge variant="default">
                ✅ Enabled
              </Badge>
              <p className="text-xs text-gray-500 mt-1">
                Multi-tenant isolation is active
              </p>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <h4 className="font-semibold">Security Features Active</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-500" />
                <span className="text-sm">Row Level Security (RLS)</span>
              </div>
              <div className="flex items-center gap-2">
                <Key className="h-4 w-4 text-green-500" />
                <span className="text-sm">Role-based Access Control</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-500" />
                <span className="text-sm">Organization Isolation</span>
              </div>
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-green-500" />
                <span className="text-sm">Subscription Validation</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team & Access Management
          </CardTitle>
          <CardDescription>
            Manage team members and their access to your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold">Team Members</h4>
                <p className="text-sm text-gray-600">
                  Users who have access to this organization
                </p>
              </div>
              {canManageTenant && (
                <Button disabled>
                  Invite Member
                </Button>
              )}
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <p className="text-sm text-gray-600">
                Team management features will be available in the next update
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Testing & Development */}
      <Card>
        <CardHeader>
          <CardTitle>Testing & Development</CardTitle>
          <CardDescription>
            Tools for testing your multi-tenant configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link href="/test-tenant">
              <Button variant="outline" className="w-full">
                Test Tenant Detection
              </Button>
            </Link>
            <Button variant="outline" className="w-full" disabled>
              View Access Logs
            </Button>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded">
            <h4 className="font-semibold text-yellow-800 mb-2">Multi-Tenant Testing</h4>
            <p className="text-sm text-yellow-700 mb-3">
              Access your organization using these URLs:
            </p>
            <ol className="space-y-3">
              <div>
                <h5 className="text-sm font-semibold text-yellow-800">Development:</h5>
                <code className="text-sm">http://{organization.slug}.localhost:3001</code>
              </div>
              
              <div>
                <h5 className="text-sm font-semibold text-yellow-800">Netlify Production:</h5>
                <code className="text-sm">https://{organization.slug}-site-name.netlify.app</code>
                <p className="text-xs text-yellow-700 mt-1">
                  Your organization will be accessible via the Netlify subdomain pattern
                </p>
              </div>

              <div>
                <h5 className="text-sm font-semibold text-yellow-800">Custom Domain (When Configured):</h5>
                <code className="text-sm">https://{organization.slug}.yourdomain.com</code>
              </div>

              <div className="mt-4">
                <p className="text-sm text-yellow-700">Test Configuration:</p>
                <ol className="list-decimal list-inside text-sm text-yellow-700 space-y-1 mt-2">
                  <li>For local: Add <code>127.0.0.1 {organization.slug}.localhost</code> to hosts file</li>
                  <li>Visit the test page at <code>/test-tenant</code> on any domain</li>
                  <li>Verify tenant detection shows correct type</li>
                </ol>
              </div>
            </ol>
          </div>
        </CardContent>
      </Card>

      {!canManageTenant && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <Shield className="h-5 w-5" />
              <span className="font-semibold">Limited Access</span>
            </div>
            <p className="text-sm text-yellow-700 mt-2">
              You have read-only access to multi-tenant settings. Contact your organization owner or admin to make changes.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 