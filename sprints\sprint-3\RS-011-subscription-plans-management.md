# RS-011: Subscription Plans Management

## Ticket Information

- **Story:** 3.2 - Subscription Plans Management
- **Priority:** Medium
- **Assignee:** Full-stack Developer
- **Estimate:** 8 points
- **Status:** 📋 **OPEN**
- **Sprint:** 3 - Subscription & Payment System

## Description

Create admin interface for managing subscription plans and pricing.

## Technical Tasks

### 🔄 Pending Tasks

- [ ] Create subscription plans database table:

  - Plan metadata storage
  - Feature flags per plan
  - Pricing information
  - Plan status and visibility

- [ ] Create `components/admin/plans-management.tsx`:

  - Plan creation form
  - Plan editing interface
  - Feature management
  - Pricing configuration

- [ ] Create plan comparison utilities:

  - Feature comparison matrix
  - Pricing display
  - Plan recommendation logic
  - Upgrade/downgrade paths

- [ ] Implement plan-based feature flags:

  - Menu item limits
  - Custom domain access
  - Analytics access
  - Advanced features

- [ ] Create `app/api/plans/route.ts`:

  - CRUD operations for plans
  - Stripe product synchronization
  - Plan validation
  - Feature flag management

- [ ] Create public pricing page:
  - Plan comparison table
  - Feature highlights
  - Call-to-action buttons
  - FAQ section

## Acceptance Criteria

- [ ] Admin can create and edit subscription plans
- [ ] Plans sync correctly with Stripe
- [ ] Feature flags work throughout the application
- [ ] Pricing page displays all plans correctly
- [ ] Plan comparison is clear and accurate
- [ ] Feature limitations are enforced

## Definition of Done

- [ ] Code is peer-reviewed and approved
- [ ] All acceptance criteria are met
- [ ] Unit tests are written and passing
- [ ] Integration tests pass
- [ ] Security requirements are satisfied
- [ ] Performance requirements are met
- [ ] Documentation is updated
- [ ] QA testing is complete

## Dependencies

- 📋 RS-010 (Webhook Event Handler) - **PENDING**

## Database Schema

```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  stripe_product_id TEXT UNIQUE NOT NULL,
  stripe_price_id TEXT UNIQUE NOT NULL,
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  features JSONB DEFAULT '{}',
  limits JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## File Structure

```
app/
├── admin/
│   └── plans/
│       └── page.tsx
├── pricing/
│   └── page.tsx
├── api/
│   └── plans/
│       └── route.ts
components/
├── admin/
│   ├── plans-management.tsx
│   ├── plan-form.tsx
│   └── feature-flags-manager.tsx
├── pricing/
│   ├── pricing-table.tsx
│   ├── plan-card.tsx
│   └── feature-comparison.tsx
lib/
├── plans.ts
├── feature-flags.ts
└── stripe-sync.ts
types/
└── plans.ts
```

## Plan Structure

### Starter Plan ($29/month)

- Basic menu management
- 1 custom menu
- Up to 50 menu items
- Standard support

### Professional Plan ($79/month)

- Advanced menu management
- Unlimited menus
- Up to 200 menu items
- Custom domain support
- Analytics dashboard
- Priority support

### Enterprise Plan ($199/month)

- Everything in Professional
- Unlimited menu items
- Advanced analytics
- Multi-location support
- API access
- Dedicated support

## Feature Flags System

```typescript
interface PlanFeatures {
  maxMenus: number;
  maxMenuItems: number;
  customDomain: boolean;
  analytics: boolean;
  apiAccess: boolean;
  multiLocation: boolean;
  prioritySupport: boolean;
}
```

## Implementation Notes

- Sync plans with Stripe products automatically
- Implement feature flag checks throughout the application
- Create upgrade/downgrade flow
- Handle proration for plan changes
- Add usage tracking for plan limits

## Security Considerations

- Admin-only access to plan management
- Validate plan changes before applying
- Secure API endpoints with proper authentication
- Audit log for plan modifications

## Testing Requirements

- [ ] Unit tests for plan CRUD operations
- [ ] Feature flag testing
- [ ] Stripe synchronization testing
- [ ] Pricing display testing
- [ ] Plan comparison testing

## Related Stories

- Story 3.2: Subscription Plans Management
- Story 3.3: Subscription Checkout Flow (depends on this)

## Next Steps After Completion

1. Implement checkout flow (RS-012)
2. Create subscription management interface (RS-013)
3. Add webhook error handling (RS-014)
