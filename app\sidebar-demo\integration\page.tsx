'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Sidebar,
  SidebarProvider,
  MobileSidebarTrigger
} from '@/components/layout/sidebar-navigation'
import {
  Code,
  Layers,
  Zap,
  CheckCircle,
  AlertCircle,
  Info,
  Copy,
  Download,
  ExternalLink
} from 'lucide-react'

const integrationSteps = [
  {
    title: 'Install Dependencies',
    description: 'Ensure you have the required dependencies',
    code: `npm install lucide-react
# or
yarn add lucide-react`,
    type: 'bash'
  },
  {
    title: 'Copy Components',
    description: 'Add the sidebar components to your project',
    code: `// Copy these files to your project:
components/layout/sidebar-navigation.tsx
components/layout/sidebar-variants.tsx`,
    type: 'text'
  },
  {
    title: 'Update Your Layout',
    description: 'Wrap your dashboard layout with SidebarProvider',
    code: `import { SidebarProvider, Sidebar, MobileSidebarTrigger } from '@/components/layout/sidebar-navigation'

export default function DashboardLayout({ children }) {
  return (
    <SidebarProvider>
      <div className="flex h-screen bg-gray-50">
        <Sidebar variant="default" />
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-white shadow-sm border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-xl font-semibold">Dashboard</h1>
              <MobileSidebarTrigger />
            </div>
          </header>
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}`,
    type: 'tsx'
  },
  {
    title: 'Customize Navigation',
    description: 'Update the navigation items to match your routes',
    code: `// In sidebar-navigation.tsx, update the navigationItems array:
export const navigationItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    badge: null,
    group: 'main'
  },
  {
    title: 'Your Custom Page',
    href: '/your-route',
    icon: YourIcon,
    badge: 'New',
    group: 'main'
  },
  // Add more items...
]`,
    type: 'tsx'
  }
]

const variants = [
  { key: 'default', name: 'Classic White', description: 'Clean professional look' },
  { key: 'modern', name: 'Dark Modern', description: 'Sleek dark theme' },
  { key: 'minimal', name: 'Minimal Gray', description: 'Subtle and minimal' },
  { key: 'glass', name: 'Glass Morphism', description: 'Modern glass effect' },
  { key: 'gradient', name: 'Brand Gradient', description: 'Colorful gradient theme' }
]

export default function SidebarIntegrationPage() {
  const [selectedVariant, setSelectedVariant] = useState('default')
  const [copiedStep, setCopiedStep] = useState<number | null>(null)

  const copyToClipboard = (text: string, stepIndex: number) => {
    navigator.clipboard.writeText(text)
    setCopiedStep(stepIndex)
    setTimeout(() => setCopiedStep(null), 2000)
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen bg-gray-50">
        <Sidebar variant={selectedVariant as any} />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <header className="bg-white shadow-sm border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Integration Guide</h1>
                <p className="text-gray-600">Learn how to integrate the sidebar into your project</p>
              </div>
              <div className="flex items-center space-x-4">
                <MobileSidebarTrigger />
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Documentation
                </Button>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              {/* Live Demo Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Zap className="h-5 w-5 text-orange-500" />
                    <span>Live Integration Demo</span>
                  </CardTitle>
                  <CardDescription>
                    This page itself demonstrates the sidebar integration. Try different variants below.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Choose Sidebar Variant:
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-5 gap-2">
                        {variants.map((variant) => (
                          <Button
                            key={variant.key}
                            variant={selectedVariant === variant.key ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSelectedVariant(variant.key)}
                            className="text-xs"
                          >
                            {variant.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start space-x-2">
                        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-900">Currently Using: {variants.find(v => v.key === selectedVariant)?.name}</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            {variants.find(v => v.key === selectedVariant)?.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Integration Steps */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Code className="h-5 w-5 text-green-500" />
                    <span>Step-by-Step Integration</span>
                  </CardTitle>
                  <CardDescription>
                    Follow these steps to integrate the sidebar into your Next.js application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {integrationSteps.map((step, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900">{step.title}</h3>
                              <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(step.code, index)}
                            className="flex items-center space-x-1"
                          >
                            {copiedStep === index ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                            <span>{copiedStep === index ? 'Copied!' : 'Copy'}</span>
                          </Button>
                        </div>
                        
                        <div className="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto">
                          <pre className="text-sm">
                            <code>{step.code}</code>
                          </pre>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Features Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Layers className="h-5 w-5 text-blue-500" />
                      <span>Key Features</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {[
                        'Fully responsive design',
                        'Collapsible navigation',
                        'Mobile-friendly overlay',
                        'Multiple theme variants',
                        'Smooth animations',
                        'TypeScript support',
                        'Customizable navigation items',
                        'Badge support for notifications'
                      ].map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5 text-amber-500" />
                      <span>Requirements</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {[
                        'Next.js 13+ with App Router',
                        'Tailwind CSS configured',
                        'shadcn/ui components',
                        'lucide-react for icons',
                        'TypeScript (recommended)',
                        'React 18+'
                      ].map((requirement, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="h-2 w-2 bg-amber-500 rounded-full" />
                          <span className="text-sm">{requirement}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Download Section */}
              <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Download className="h-5 w-5 text-orange-600" />
                    <span>Ready to Use</span>
                  </CardTitle>
                  <CardDescription>
                    All components are ready for production use in your Next.js application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">
                        The sidebar components are fully self-contained and can be easily customized to match your brand.
                      </p>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">TypeScript</Badge>
                        <Badge variant="secondary">Responsive</Badge>
                        <Badge variant="secondary">Accessible</Badge>
                        <Badge variant="secondary">Customizable</Badge>
                      </div>
                    </div>
                    <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                      <Download className="h-4 w-4 mr-2" />
                      Get Started
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}
