import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST() {
  try {
    const supabase = createClient()

    // Sign out from Supabase Auth - this will invalidate the session
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error('Sign out error:', error)
      return NextResponse.json(
        { error: 'Failed to sign out' },
        { status: 500 }
      )
    }

    // Create response with redirect to home page
    const response = NextResponse.json({
      message: 'Signed out successfully',
      redirectTo: '/',
    })

    // Clear any additional cookies if needed
    // Supabase auth cookies are automatically handled by the client
    
    return response

  } catch (error) {
    console.error('Sign out error:', error)
    return NextResponse.json(
      { error: 'Internal server error during sign out' },
      { status: 500 }
    )
  }
}

// Also handle GET requests for direct logout URLs
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Sign out from Supabase Auth
    await supabase.auth.signOut()
    
    // Redirect to home page
    const url = request.nextUrl.clone()
    url.pathname = '/'
    url.searchParams.delete('logout') // Clean up any logout params
    
    return NextResponse.redirect(url)
    
  } catch (error) {
    console.error('Sign out error:', error)
    
    // Even if there's an error, redirect to home page
    const url = request.nextUrl.clone()
    url.pathname = '/'
    return NextResponse.redirect(url)
  }
} 