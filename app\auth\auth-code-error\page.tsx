import Link from "next/link"
import { AlertCircle } from "lucide-react"
import { AuthLayout } from "@/components/auth/auth-layout"
import { Button } from "@/components/ui/button"

export default function AuthCodeErrorPage() {
  return (
    <AuthLayout
      title="Authentication Error"
      description="There was a problem with your authentication"
      showBackToHome={false}
    >
      <div className="space-y-6 text-center">
        <div className="flex justify-center">
          <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-gray-900">Authentication failed</h3>
          <p className="text-gray-600">
            The authentication link you used is invalid or has expired. This could happen if:
          </p>
          <ul className="text-sm text-gray-600 text-left space-y-1 mt-4">
            <li>• The link has already been used</li>
            <li>• The link has expired (links are valid for 24 hours)</li>
            <li>• The link was corrupted during transmission</li>
          </ul>
        </div>
        <div className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/login">
              Try signing in again
            </Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/reset-password">
              Request a new password reset
            </Link>
          </Button>
          <div className="text-sm text-gray-600">
            Need help?{" "}
            <Link
              href="/contact"
              className="text-orange-600 hover:text-orange-500 font-medium transition-colors"
            >
              Contact support
            </Link>
          </div>
        </div>
      </div>
    </AuthLayout>
  )
} 