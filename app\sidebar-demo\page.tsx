'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Palette,
  Eye,
  Code,
  Rocket,
  ArrowRight,
  Star,
  Zap,
  Sparkles,
  Crown,
  Gem,
  Layers,
  Settings,
  ExternalLink
} from 'lucide-react'

const demoPages = [
  {
    title: 'Basic Collection',
    description: 'Essential sidebar designs for everyday use',
    href: '/sidebar-demo/basic',
    icon: Palette,
    color: 'bg-blue-500',
    features: ['5 Classic Variants', 'Clean Designs', 'Professional Look', 'Easy Integration'],
    badge: 'Popular'
  },
  {
    title: 'Advanced Collection',
    description: 'Cutting-edge designs with premium features',
    href: '/sidebar-demo/advanced',
    icon: Rocket,
    color: 'bg-purple-500',
    features: ['10 Total Variants', 'Advanced Effects', 'Premium Features', 'Animations'],
    badge: 'New'
  },
  {
    title: 'Integration Guide',
    description: 'Learn how to implement in your project',
    href: '/sidebar-demo/integration',
    icon: Code,
    color: 'bg-green-500',
    features: ['Step-by-Step', 'Code Examples', 'Live Demo', 'Best Practices'],
    badge: 'Guide'
  }
]

const quickPreviewVariants = [
  { key: 'default', name: 'Classic', icon: Palette, color: 'bg-white border' },
  { key: 'modern', name: 'Dark', icon: Zap, color: 'bg-slate-900' },
  { key: 'glass', name: 'Glass', icon: Gem, color: 'bg-white/80 backdrop-blur' },
  { key: 'gradient', name: 'Gradient', icon: Crown, color: 'bg-gradient-to-br from-orange-500 to-red-600' }
]

export default function SidebarDemoPage() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-6">
          <div className="flex items-center justify-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <Layers className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Sidebar Gallery
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover stunning, production-ready sidebar navigation components for your Next.js applications.
            Choose from multiple collections ranging from classic to cutting-edge designs.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Badge variant="secondary" className="text-sm px-3 py-1">
              10+ Variants
            </Badge>
            <Badge variant="secondary" className="text-sm px-3 py-1">
              TypeScript Ready
            </Badge>
            <Badge variant="secondary" className="text-sm px-3 py-1">
              Fully Responsive
            </Badge>
          </div>
        </div>

        {/* Demo Collections */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {demoPages.map((page) => {
            const Icon = page.icon

            return (
              <Card
                key={page.href}
                className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 border-0 shadow-lg"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className={`h-12 w-12 rounded-xl ${page.color} flex items-center justify-center shadow-lg`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {page.badge}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                      {page.title}
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      {page.description}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    {page.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Star className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link href={page.href}>
                    <Button className="w-full group-hover:bg-blue-600 transition-colors">
                      Explore Collection
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Preview */}
        <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-center justify-center">
              <Eye className="h-5 w-5 text-blue-600" />
              <span>Quick Preview</span>
            </CardTitle>
            <CardDescription className="text-center">
              Get a taste of the different sidebar styles available
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {quickPreviewVariants.map((variant) => {
                const Icon = variant.icon
                return (
                  <div key={variant.key} className="text-center space-y-2">
                    <div className={`h-20 w-full rounded-lg ${variant.color} flex items-center justify-center shadow-sm border`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <p className="text-sm font-medium text-gray-700">{variant.name}</p>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              icon: Zap,
              title: 'Lightning Fast',
              description: 'Optimized for performance with smooth animations'
            },
            {
              icon: Settings,
              title: 'Highly Customizable',
              description: 'Easy to customize colors, icons, and layout'
            },
            {
              icon: Sparkles,
              title: 'Modern Design',
              description: 'Contemporary UI patterns and best practices'
            },
            {
              icon: ExternalLink,
              title: 'Production Ready',
              description: 'Battle-tested components ready for deployment'
            }
          ].map((feature, index) => {
            const Icon = feature.icon
            return (
              <Card key={index} className="text-center border-0 shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mx-auto">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-xl">
          <CardContent className="text-center py-12">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl mb-8 text-blue-100">
              Choose your favorite collection and start building amazing user interfaces
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sidebar-demo/advanced">
                <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                  <Rocket className="h-5 w-5 mr-2" />
                  View All Variants
                </Button>
              </Link>
              <Link href="/sidebar-demo/integration">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                  <Code className="h-5 w-5 mr-2" />
                  Integration Guide
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
