# Netlify Deployment Guide

This guide walks you through deploying the Bezorg restaurant menu platform to Netlify.

## Prerequisites

1. **GitHub Repository**: Ensure your code is pushed to GitHub
2. **Supabase Project**: Set up and configured (see `SUPABASE_SETUP.md`)
3. **Stripe Account**: Set up for payments (production keys required)
4. **Netlify Account**: Create account at [netlify.com](https://netlify.com)

## Step 1: Connect Repository to Netlify

1. Log in to your Netlify account
2. Click "New site from Git"
3. Choose GitHub and authorize Netlify
4. Select your `bezorg` repository
5. Configure build settings:
   - **Branch to deploy**: `main`
   - **Build command**: `npm run build`
   - **Publish directory**: `.next`

## Step 2: Configure Environment Variables

In your Netlify site settings, go to "Environment variables" and add the following:

### Required Variables

```bash
# Database - Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Stripe - Production Keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Application URLs
NEXT_PUBLIC_APP_URL=https://your-netlify-site.netlify.app
NEXT_PUBLIC_API_URL=https://your-netlify-site.netlify.app/api

# JWT Secret
JWT_SECRET=your-very-secure-jwt-secret-for-production
```

### Optional Variables

```bash
# Email Service
RESEND_API_KEY=your-resend-api-key
FROM_EMAIL=<EMAIL>

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Error Monitoring
SENTRY_DSN=https://<EMAIL>/project-id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS=true
NEXT_PUBLIC_MAINTENANCE_MODE=false
```

## Step 3: Configure Netlify Plugins

The `netlify.toml` file is already configured with the Next.js plugin. Netlify will automatically install it.

## Step 4: Deploy

1. Click "Deploy site" in Netlify
2. Wait for the build to complete (usually 2-5 minutes)
3. Check the deploy log for any errors

## Step 5: Configure Custom Domain (Optional)

1. In Netlify, go to "Domain settings"
2. Click "Add custom domain"
3. Enter your domain name
4. Follow DNS configuration instructions
5. Enable HTTPS (automatically handled by Netlify)

## Step 6: Set up Stripe Webhooks

1. In your Stripe dashboard, go to "Webhooks"
2. Click "Add endpoint"
3. Use URL: `https://your-domain.com/api/webhooks/stripe`
4. Select events to listen for:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copy the webhook secret to your Netlify environment variables

## Step 7: Configure Supabase for Production

1. In Supabase, go to "Settings" > "API"
2. Add your Netlify domain to "Site URL"
3. Add your domain to "Redirect URLs":
   - `https://your-domain.com/auth/callback`
   - `https://your-domain.com/auth/confirm`

## Step 8: Test the Deployment

1. Visit your deployed site
2. Test user registration/login
3. Test organization creation
4. Verify email confirmations work
5. Test the payment flow (use Stripe test mode first)

## Troubleshooting

### Build Errors

**Missing environment variables:**

- Check all required variables are set in Netlify
- Ensure values don't have extra spaces or quotes

**TypeScript errors:**

- Check the build log for specific error messages
- Ensure all dependencies are properly installed

### Runtime Errors

**Database connection issues:**

- Verify Supabase URL and keys
- Check RLS policies are properly set up
- Ensure database tables exist

**Authentication issues:**

- Verify Supabase auth configuration
- Check redirect URLs are correctly set
- Ensure JWT secret is properly configured

**Payment issues:**

- Verify Stripe keys are correct
- Check webhook endpoint is accessible
- Ensure webhook secret matches

### Performance Issues

**Slow loading:**

- Enable image optimization in `next.config.js`
- Check for large bundle sizes
- Optimize database queries

## Monitoring and Maintenance

### Analytics

1. Set up Google Analytics (optional)
2. Configure Sentry for error tracking (optional)
3. Monitor Netlify analytics

### Updates

1. All updates are deployed automatically from the `main` branch
2. Use feature branches for testing changes
3. Monitor deploy previews for branches

### Backup

1. Regular database backups via Supabase
2. Keep environment variables documented
3. Monitor uptime and performance

## Security Checklist

- [ ] All environment variables are set securely
- [ ] HTTPS is enabled and enforced
- [ ] Stripe webhooks are properly secured
- [ ] Database RLS policies are active
- [ ] Security headers are configured (via `netlify.toml`)
- [ ] Regular security updates are applied

## Support

For deployment issues:

1. Check Netlify deploy logs
2. Review Supabase logs
3. Check Stripe webhook logs
4. Refer to the project documentation
