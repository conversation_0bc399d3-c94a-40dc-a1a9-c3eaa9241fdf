import { NextRequest, NextResponse } from 'next/server'
import { isSlugAvailable } from '@/lib/services/organization'
import { slugCheckSchema } from '@/lib/validations/organization'
import { validateSlugFormat, generateSlugSuggestions } from '@/lib/utils/slug'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const result = slugCheckSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Invalid slug format', 
          details: result.error.errors 
        },
        { status: 400 }
      )
    }

    const { slug } = result.data
    
    // First validate the slug format
    const validation = validateSlugFormat(slug)
    if (!validation.valid) {
      return NextResponse.json({
        available: false,
        error: validation.error,
        suggestions: []
      })
    }

    // Check if slug is available
    const available = await isSlugAvailable(slug)
    
    let suggestions: string[] = []
    if (!available) {
      // Generate alternative suggestions if slug is taken
      try {
        suggestions = await generateSlugSuggestions(slug, isSlugAvailable)
      } catch (error) {
        console.error('Error generating slug suggestions:', error)
        // Continue without suggestions if generation fails
      }
    }

    return NextResponse.json({
      available,
      suggestions
    })

  } catch (error) {
    console.error('Error checking slug availability:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 