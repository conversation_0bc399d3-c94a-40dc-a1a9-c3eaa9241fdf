'use client';
/* eslint-disable react/no-unescaped-entities */


import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { SettingCategory } from '@/types/settings';
import { 
  Plug, 
  CreditCard, 
  Mail, 
  MessageSquare, 
  BarChart3, 
  Cloud, 
  RotateCcw,
  CheckCircle,

  ExternalLink,
  Plus
} from 'lucide-react';

interface IntegrationSettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  onReset: () => void;
}

export function IntegrationSettings({
  getSetting,
  updateSetting,
  onReset,
}: IntegrationSettingsProps) {
  // Get current settings with defaults
  const integrations = getSetting('integrations', 'settings') || {
    stripe: {
      enabled: false,
      testMode: true,
      webhookUrl: '',
    },
    email: {
      provider: 'smtp',
      settings: {},
    },
    sms: {
      provider: 'twilio',
      settings: {},
    },
    analytics: {
      googleAnalytics: {
        enabled: false,
        trackingId: '',
      },
      customEvents: false,
    },
    storage: {
      provider: 'supabase',
      settings: {},
    },
  };

  const updateStripe = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      stripe: {
        ...integrations.stripe,
        [field]: value,
      },
    });
  };

  const updateEmail = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      email: {
        ...integrations.email,
        [field]: value,
      },
    });
  };

  const updateSms = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      sms: {
        ...integrations.sms,
        [field]: value,
      },
    });
  };

  const updateAnalytics = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      analytics: {
        ...integrations.analytics,
        [field]: value,
      },
    });
  };

  const updateGoogleAnalytics = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      analytics: {
        ...integrations.analytics,
        googleAnalytics: {
          ...integrations.analytics.googleAnalytics,
          [field]: value,
        },
      },
    });
  };

  const updateStorage = async (field: string, value: any) => {
    await updateSetting('integrations', 'settings', {
      ...integrations,
      storage: {
        ...integrations.storage,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Payment Processing */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Processing
              </CardTitle>
              <CardDescription>
                Configure payment processing with Stripe.
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {integrations.stripe.enabled && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              )}
              <Button variant="outline" size="sm" onClick={onReset}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Enable Stripe Integration</Label>
              <p className="text-sm text-muted-foreground">
                Accept payments and manage subscriptions
              </p>
            </div>
            <Switch
              checked={integrations.stripe.enabled}
              onCheckedChange={(checked) => updateStripe('enabled', checked)}
            />
          </div>

          {integrations.stripe.enabled && (
            <>
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="font-normal">Test Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Use Stripe&apos;s test environment for development
                    </p>
                  </div>
                  <Switch
                    checked={integrations.stripe.testMode}
                    onCheckedChange={(checked) => updateStripe('testMode', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="webhookUrl">Webhook URL</Label>
                  <Input
                    id="webhookUrl"
                    value={integrations.stripe.webhookUrl}
                    onChange={(e) => updateStripe('webhookUrl', e.target.value)}
                    placeholder="https://your-domain.com/api/webhooks/stripe"
                  />
                  <p className="text-xs text-muted-foreground">
                    Configure this URL in your Stripe dashboard to receive webhook events.
                  </p>
                </div>

                <Button variant="outline" className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Stripe Dashboard
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Email Service */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Service
          </CardTitle>
          <CardDescription>
            Configure email delivery for notifications and transactional emails.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Email Provider</Label>
            <Select
              value={integrations.email.provider}
              onValueChange={(value) => updateEmail('provider', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="smtp">SMTP</SelectItem>
                <SelectItem value="sendgrid">SendGrid</SelectItem>
                <SelectItem value="mailgun">Mailgun</SelectItem>
                <SelectItem value="ses">Amazon SES</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Configuration Required:</strong> Email provider settings need to be configured 
              in your environment variables for security.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* SMS Service */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            SMS Service
          </CardTitle>
          <CardDescription>
            Configure SMS delivery for critical notifications.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>SMS Provider</Label>
            <Select
              value={integrations.sms.provider}
              onValueChange={(value) => updateSms('provider', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="twilio">Twilio</SelectItem>
                <SelectItem value="aws-sns">AWS SNS</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <strong>Cost Notice:</strong> SMS services incur per-message charges. 
              Monitor usage to control costs.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics & Tracking
          </CardTitle>
          <CardDescription>
            Configure analytics and user behavior tracking.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Google Analytics</Label>
              <p className="text-sm text-muted-foreground">
                Track user behavior and site performance
              </p>
            </div>
            <Switch
              checked={integrations.analytics.googleAnalytics.enabled}
              onCheckedChange={(checked) => updateGoogleAnalytics('enabled', checked)}
            />
          </div>

          {integrations.analytics.googleAnalytics.enabled && (
            <div className="space-y-2">
              <Label htmlFor="trackingId">Tracking ID</Label>
              <Input
                id="trackingId"
                value={integrations.analytics.googleAnalytics.trackingId}
                onChange={(e) => updateGoogleAnalytics('trackingId', e.target.value)}
                placeholder="G-XXXXXXXXXX or UA-XXXXXXXXX-X"
              />
            </div>
          )}

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Custom Event Tracking</Label>
              <p className="text-sm text-muted-foreground">
                Track custom business events and user actions
              </p>
            </div>
            <Switch
              checked={integrations.analytics.customEvents}
              onCheckedChange={(checked) => updateAnalytics('customEvents', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* File Storage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            File Storage
          </CardTitle>
          <CardDescription>
            Configure where files and media are stored.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Storage Provider</Label>
            <Select
              value={integrations.storage.provider}
              onValueChange={(value) => updateStorage('provider', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="supabase">Supabase Storage</SelectItem>
                <SelectItem value="aws-s3">Amazon S3</SelectItem>
                <SelectItem value="cloudinary">Cloudinary</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="p-3 border rounded-lg text-center">
              <div className="font-medium">Current Usage</div>
              <div className="text-2xl font-bold text-primary">2.4 GB</div>
              <div className="text-sm text-muted-foreground">of 10 GB</div>
            </div>
            <div className="p-3 border rounded-lg text-center">
              <div className="font-medium">Files Stored</div>
              <div className="text-2xl font-bold text-primary">1,247</div>
              <div className="text-sm text-muted-foreground">total files</div>
            </div>
            <div className="p-3 border rounded-lg text-center">
              <div className="font-medium">Bandwidth</div>
              <div className="text-2xl font-bold text-primary">45 GB</div>
              <div className="text-sm text-muted-foreground">this month</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Webhooks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plug className="h-5 w-5" />
            Webhooks
          </CardTitle>
          <CardDescription>
            Configure webhooks to receive real-time notifications about events.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Plug className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-medium mb-2">Webhook Management</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Set up webhooks to receive notifications when events occur in your account.
            </p>
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Webhook
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 