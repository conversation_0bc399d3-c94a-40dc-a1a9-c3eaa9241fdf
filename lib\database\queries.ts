import { createClient } from '@/lib/supabase/server'
import { createServiceClient } from '@/lib/supabase/server'
import { createClient as createBrowserClient } from '@/lib/supabase/client'
import { Database } from '@/types/database'

type Tables = Database['public']['Tables']
type Organization = Tables['organizations']['Row']
type User = Tables['users']['Row']
type Menu = Tables['menus']['Row']
type MenuCategory = Tables['menu_categories']['Row']
type MenuItem = Tables['menu_items']['Row']

// Server-side queries using service role for admin operations
export async function getOrganizations() {
  const supabase = createServiceClient() // Use service role to bypass RLS for admin operations
  const { data, error } = await supabase
    .from('organizations')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching organizations:', error)
    return []
  }

  return data as Organization[]
}

export async function getOrganizationBySlug(slug: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('organizations')
    .select('*')
    .eq('slug', slug)
    .single()

  if (error) {
    console.error('Error fetching organization:', error)
    return null
  }

  return data as Organization
}

export async function getOrganizationMenus(organizationId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('menus')
    .select(`
      *,
      menu_categories (
        *,
        menu_items (*)
      )
    `)
    .eq('organization_id', organizationId)
    .eq('is_active', true)
    .order('display_order', { ascending: true })

  if (error) {
    console.error('Error fetching menus:', error)
    return []
  }

  return data
}

export async function getPublishedMenuBySlug(orgSlug: string) {
  const supabase = createClient()
  
  // First get the organization
  const { data: org, error: orgError } = await supabase
    .from('organizations')
    .select('id')
    .eq('slug', orgSlug)
    .single()

  if (orgError || !org) {
    console.error('Error fetching organization:', orgError)
    return null
  }

  // Then get the published menu with categories and items
  const { data, error } = await supabase
    .from('menus')
    .select(`
      *,
      menu_categories!inner (
        *,
        menu_items!inner (*)
      )
    `)
    .eq('organization_id', org.id)
    .eq('is_published', true)
    .eq('is_active', true)
    .eq('menu_categories.is_active', true)
    .eq('menu_categories.menu_items.is_available', true)
    .order('display_order', { ascending: true })
    .order('menu_categories.display_order', { ascending: true })
    .order('menu_categories.menu_items.display_order', { ascending: true })
    .single()

  if (error) {
    console.error('Error fetching published menu:', error)
    return null
  }

  return data
}

// Client-side queries for browser usage
export async function getOrganizationsClient() {
  const supabase = createBrowserClient()
  const { data, error } = await supabase
    .from('organizations')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching organizations:', error)
    return []
  }

  return data as Organization[]
}

// Utility function to test database connection
export async function testDatabaseConnection() {
  try {
    const supabase = createServiceClient() // Use service role for testing
    
    // Test basic connection
    const { data, error } = await supabase
      .from('organizations')
      .select('id, name, slug, subscription_status')
      .limit(3)

    if (error) {
      console.error('Database connection test failed:', error)
      return { 
        success: false, 
        error: `Database error: ${error.message}`,
        details: error 
      }
    }

    return { 
      success: true, 
      message: `Database connection successful. Found ${data?.length || 0} organizations.`,
      sampleData: data,
      connectionType: 'Service Role (Admin)'
    }
  } catch (error) {
    console.error('Database connection test error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown connection error',
      details: error
    }
  }
}

// Additional utility functions for admin operations
export async function createSampleOrganization() {
  try {
    const supabase = createServiceClient()
    
    const { data, error } = await supabase
      .from('organizations')
      .insert({
        name: 'Test Restaurant',
        slug: 'test-restaurant',
        description: 'A test restaurant for development',
        contact_email: '<EMAIL>',
        subscription_status: 'trial',
        plan_id: 'basic'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating sample organization:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error creating sample organization:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

// Test all table access
export async function testAllTablesAccess() {
  try {
    const supabase = createServiceClient()
    const results: Record<string, any> = {}
    
    // Test each table
    const tables = [
      'organizations',
      'users', 
      'menus',
      'menu_categories',
      'menu_items',
      'subscriptions',
      'subscription_items',
      'invoices'
    ] as const
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1)
        
        results[table] = {
          success: !error,
          error: error?.message,
          recordCount: data?.length || 0
        }
      } catch (err) {
        results[table] = {
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        }
      }
    }
    
    return { success: true, results }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Export types for use in components
export type { Organization, User, Menu, MenuCategory, MenuItem } 