import { Shield, ArrowLeft, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="rounded-full bg-yellow-100 p-3">
            <Shield className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Access Denied
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          You don&apos;t have permission to access this organization
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                What happened?
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>You&apos;re trying to access an organization that you&apos;re not a member of.</p>
                <p>This could be because:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>You&apos;re not invited to this organization</li>
                  <li>Your membership has been revoked</li>
                  <li>The organization URL is incorrect</li>
                  <li>You&apos;re logged into the wrong account</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <Button
                asChild
                className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
              >
                <Link href="/dashboard">
                  <Home className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                className="w-full"
              >
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                Think this is a mistake? Contact the organization admin or our{' '}
                <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-500">
                  support team
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 