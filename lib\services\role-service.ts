import { 
  Role, 
  UserRole, 
  CreateRoleData, 
  UpdateRoleData, 
  AssignRoleData,
  SYSTEM_ROLES,
  PERMISSIONS 
} from '@/types/roles';
import { 
  getRoles,
  createRole as createRoleQuery,
  updateRole as updateRoleQuery,
  delete<PERSON><PERSON> as deleteRoleQuery,
  getUser<PERSON><PERSON><PERSON>,
  getUserRolesByUserId,
  assign<PERSON><PERSON> as assignRoleQuery,
  removeRole as removeRoleQuery,
  getUserPermissions,
  getRoleAuditLog,
  RoleQueryError
} from '@/lib/db/role-queries';
import { checkUserPermission } from '@/lib/auth/permissions';

export class RoleServiceError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RoleServiceError';
  }
}

/**
 * Initialize system roles for an organization
 */
export async function initializeSystemRoles(organizationId: string, createdBy: string): Promise<Role[]> {
  const roles: Role[] = [];
  
  for (const [, roleData] of Object.entries(SYSTEM_ROLES)) {
    try {
      const role = await createRoleQuery(organizationId, roleData, createdBy);
      roles.push(role);
    } catch (error) {
      if (error instanceof RoleQueryError && error.code === '23505') {
        // Role already exists, skip
        continue;
      }
      throw error;
    }
  }
  
  return roles;
}

/**
 * Get all roles for an organization
 */
export async function getOrganizationRoles(
  organizationId: string,
  userId: string
): Promise<Role[]> {
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.ROLE_READ
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to view roles', 'FORBIDDEN');
  }
  
  return await getRoles(organizationId);
}

/**
 * Create a new role
 */
export async function createOrganizationRole(
  organizationId: string,
  roleData: CreateRoleData,
  userId: string
): Promise<Role> {
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.ROLE_CREATE
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to create roles', 'FORBIDDEN');
  }
  
  return await createRoleQuery(organizationId, roleData, userId);
}

/**
 * Update an existing role
 */
export async function updateOrganizationRole(
  roleId: string,
  organizationId: string,
  updates: UpdateRoleData,
  userId: string
): Promise<Role> {
  // Check if user has permission to update roles
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.ROLE_UPDATE
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to update roles', 'FORBIDDEN');
  }
  
  // Validate permissions if provided
  if (updates.permissions) {
    const validPermissions = Object.values(PERMISSIONS);
    for (const permission of updates.permissions) {
      const isValid = validPermissions.some(
        p => p.resource === permission.resource && p.action === permission.action
      );
      if (!isValid) {
        throw new RoleServiceError(
          `Invalid permission: ${permission.resource}:${permission.action}`,
          'INVALID_PERMISSION'
        );
      }
    }
  }
  
  return await updateRoleQuery(roleId, organizationId, updates, userId);
}

/**
 * Delete a role
 */
export async function deleteOrganizationRole(
  roleId: string,
  organizationId: string,
  userId: string
): Promise<void> {
  // Check if user has permission to delete roles
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.ROLE_DELETE
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to delete roles', 'FORBIDDEN');
  }
  
  return await deleteRoleQuery(roleId, organizationId, userId);
}

/**
 * Get user role assignments for an organization
 */
export async function getOrganizationUserRoles(
  organizationId: string,
  userId: string
): Promise<UserRole[]> {
  // Check if user has permission to view user roles
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.USER_VIEW
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to view user roles', 'FORBIDDEN');
  }
  
  return await getUserRoles(organizationId);
}

/**
 * Assign a role to a user
 */
export async function assignUserRole(
  assignmentData: AssignRoleData,
  organizationId: string,
  assignedBy: string
): Promise<UserRole> {
  // Check if user has permission to assign roles
  const hasPermission = await checkUserPermission(
    assignedBy,
    organizationId,
    PERMISSIONS.ROLE_ASSIGN
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to assign roles', 'FORBIDDEN');
  }
  
  return await assignRoleQuery(
    assignmentData.userId,
    assignmentData.roleId,
    organizationId,
    assignedBy,
    assignmentData.expiresAt
  );
}

/**
 * Remove a role from a user
 */
export async function removeUserRole(
  userId: string,
  roleId: string,
  organizationId: string,
  removedBy: string
): Promise<void> {
  // Check if user has permission to remove roles
  const hasPermission = await checkUserPermission(
    removedBy,
    organizationId,
    PERMISSIONS.ROLE_ASSIGN
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to remove roles', 'FORBIDDEN');
  }
  
  return await removeRoleQuery(userId, roleId, organizationId, removedBy);
}

/**
 * Get roles for a specific user
 */
export async function getUserRolesById(
  targetUserId: string,
  organizationId: string,
  requestingUserId: string
): Promise<UserRole[]> {
  // Check if user has permission to view user roles
  const hasPermission = await checkUserPermission(
    requestingUserId,
    organizationId,
    PERMISSIONS.USER_VIEW
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to view user roles', 'FORBIDDEN');
  }
  
  return await getUserRolesByUserId(targetUserId, organizationId);
}

/**
 * Get user permissions
 */
export async function getUserPermissionsById(
  targetUserId: string,
  organizationId: string,
  requestingUserId: string
) {
  // Check if user has permission to view user permissions
  const hasPermission = await checkUserPermission(
    requestingUserId,
    organizationId,
    PERMISSIONS.USER_VIEW
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to view user permissions', 'FORBIDDEN');
  }
  
  return await getUserPermissions(targetUserId, organizationId);
}

/**
 * Get role audit log
 */
export async function getRoleAuditLogForOrganization(
  organizationId: string,
  userId: string,
  limit: number = 50,
  offset: number = 0
) {
  // Check if user has permission to view audit logs
  const hasPermission = await checkUserPermission(
    userId,
    organizationId,
    PERMISSIONS.ROLE_READ
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to view audit logs', 'FORBIDDEN');
  }
  
  return await getRoleAuditLog(organizationId, limit, offset);
}

/**
 * Bulk assign roles to multiple users
 */
export async function bulkAssignRoles(
  userIds: string[],
  roleId: string,
  organizationId: string,
  assignedBy: string,
  expiresAt?: string
): Promise<UserRole[]> {
  // Check if user has permission to assign roles
  const hasPermission = await checkUserPermission(
    assignedBy,
    organizationId,
    PERMISSIONS.ROLE_ASSIGN
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to assign roles', 'FORBIDDEN');
  }
  
  const results: UserRole[] = [];
  const errors: string[] = [];
  
  for (const userId of userIds) {
    try {
      const result = await assignRoleQuery(userId, roleId, organizationId, assignedBy, expiresAt);
      results.push(result);
    } catch (error) {
      if (error instanceof RoleQueryError && error.code === 'ALREADY_ASSIGNED') {
        // Skip users who already have the role
        continue;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Failed to assign role to user ${userId}: ${errorMessage}`);
    }
  }
  
  if (errors.length > 0 && results.length === 0) {
    throw new RoleServiceError(`Bulk assignment failed: ${errors.join(', ')}`, 'BULK_ASSIGNMENT_FAILED');
  }
  
  return results;
}

/**
 * Bulk remove roles from multiple users
 */
export async function bulkRemoveRoles(
  userIds: string[],
  roleId: string,
  organizationId: string,
  removedBy: string
): Promise<void> {
  // Check if user has permission to remove roles
  const hasPermission = await checkUserPermission(
    removedBy,
    organizationId,
    PERMISSIONS.ROLE_ASSIGN
  );
  
  if (!hasPermission) {
    throw new RoleServiceError('Insufficient permissions to remove roles', 'FORBIDDEN');
  }
  
  const errors: string[] = [];
  
  for (const userId of userIds) {
    try {
      await removeRoleQuery(userId, roleId, organizationId, removedBy);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Failed to remove role from user ${userId}: ${errorMessage}`);
    }
  }
  
  if (errors.length > 0) {
    throw new RoleServiceError(`Bulk removal failed: ${errors.join(', ')}`, 'BULK_REMOVAL_FAILED');
  }
} 