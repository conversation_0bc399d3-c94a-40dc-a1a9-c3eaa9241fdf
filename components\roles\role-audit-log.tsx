'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Search, History } from 'lucide-react';
import { RoleAuditEntry } from '@/types/roles';

export function RoleAuditLog() {
  const [auditEntries, setAuditEntries] = useState<RoleAuditEntry[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAuditLog();
  }, []);

  const fetchAuditLog = async () => {
    try {
      setLoading(true);
      // API call would go here
      setAuditEntries([]);
    } catch (error) {
      console.error('Failed to fetch audit log:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionBadgeColor = (action: string) => {
    const colorMap: Record<string, string> = {
      assigned: 'bg-green-100 text-green-800',
      removed: 'bg-red-100 text-red-800',
      modified: 'bg-blue-100 text-blue-800',
      role_created: 'bg-purple-100 text-purple-800',
      role_updated: 'bg-yellow-100 text-yellow-800',
      role_deleted: 'bg-red-100 text-red-800',
    };
    return colorMap[action] || 'bg-gray-100 text-gray-800';
  };

  const formatAction = (action: string) => {
    const actionMap: Record<string, string> = {
      assigned: 'Role Assigned',
      removed: 'Role Removed',
      modified: 'Role Modified',
      role_created: 'Role Created',
      role_updated: 'Role Updated',
      role_deleted: 'Role Deleted',
    };
    return actionMap[action] || action;
  };

  const filteredEntries = auditEntries.filter(entry =>
    formatAction(entry.action).toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Role Audit Log
        </CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search audit log..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>
      <CardContent>
        {filteredEntries.length === 0 ? (
          <div className="text-center py-8">
            <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No audit entries found.</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>
                    <Badge 
                      variant="secondary"
                      className={getActionBadgeColor(entry.action)}
                    >
                      {formatAction(entry.action)}
                    </Badge>
                  </TableCell>
                  <TableCell>{entry.userId}</TableCell>
                  <TableCell>{entry.targetUserId || entry.roleId || '-'}</TableCell>
                  <TableCell>
                    {new Date(entry.createdAt).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
} 