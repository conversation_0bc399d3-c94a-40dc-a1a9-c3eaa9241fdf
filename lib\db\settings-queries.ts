import { createClient } from '@/lib/supabase/server';
import { Setting, SettingCategory, SettingsUpdatePayload } from '@/types/settings';

export class SettingsQueryError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'SettingsQueryError';
  }
}

// Helper function to convert null to undefined
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

/**
 * Get all settings for a user and organization
 */
export async function getUserSettings(
  userId: string,
  organizationId: string
): Promise<Setting[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .eq('organization_id', organizationId)
    .or(`user_id.eq.${userId},is_global.eq.true`)
    .order('category');

  if (error) {
    throw new SettingsQueryError(`Failed to fetch user settings: ${error.message}`, error.code);
  }

  return (data || []).map(setting => ({
    id: setting.id,
    organizationId: setting.organization_id,
    userId: nullToUndefined(setting.user_id),
    category: setting.category as SettingCategory,
    key: setting.key,
    value: setting.value,
    isGlobal: setting.is_global,
    createdAt: setting.created_at,
    updatedAt: setting.updated_at,
  }));
}

/**
 * Get organization-wide settings
 */
export async function getOrganizationSettings(organizationId: string): Promise<Setting[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .eq('organization_id', organizationId)
    .eq('is_global', true)
    .order('category');

  if (error) {
    throw new SettingsQueryError(`Failed to fetch organization settings: ${error.message}`, error.code);
  }

  return (data || []).map(setting => ({
    id: setting.id,
    organizationId: setting.organization_id,
    userId: nullToUndefined(setting.user_id),
    category: setting.category as SettingCategory,
    key: setting.key,
    value: setting.value,
    isGlobal: setting.is_global,
    createdAt: setting.created_at,
    updatedAt: setting.updated_at,
  }));
}

/**
 * Get settings by category
 */
export async function getSettingsByCategory(
  userId: string,
  organizationId: string,
  category: SettingCategory
): Promise<Setting[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .eq('organization_id', organizationId)
    .eq('category', category)
    .or(`user_id.eq.${userId},is_global.eq.true`)
    .order('key');

  if (error) {
    throw new SettingsQueryError(`Failed to fetch settings by category: ${error.message}`, error.code);
  }

  return (data || []).map(setting => ({
    id: setting.id,
    organizationId: setting.organization_id,
    userId: nullToUndefined(setting.user_id),
    category: setting.category as SettingCategory,
    key: setting.key,
    value: setting.value,
    isGlobal: setting.is_global,
    createdAt: setting.created_at,
    updatedAt: setting.updated_at,
  }));
}

/**
 * Get a specific setting value
 */
export async function getSettingValue(
  userId: string,
  organizationId: string,
  category: SettingCategory,
  key: string
): Promise<any> {
  const supabase = createClient();
  
  // First try to get user-specific setting
  const { data: userSetting } = await supabase
    .from('settings')
    .select('value')
    .eq('organization_id', organizationId)
    .eq('user_id', userId)
    .eq('category', category)
    .eq('key', key)
    .single();

  if (userSetting) {
    return userSetting.value;
  }

  // Fall back to global setting
  const { data: globalSetting, error } = await supabase
    .from('settings')
    .select('value')
    .eq('organization_id', organizationId)
    .is('user_id', null)
    .eq('category', category)
    .eq('key', key)
    .eq('is_global', true)
    .single();

  if (error && error.code !== 'PGRST116') {
    throw new SettingsQueryError(`Failed to fetch setting value: ${error.message}`, error.code);
  }

  return globalSetting?.value;
}

/**
 * Update or create a setting
 */
export async function updateSetting(
  userId: string,
  organizationId: string,
  payload: SettingsUpdatePayload
): Promise<Setting> {
  const supabase = createClient();
  
  const settingData = {
    organization_id: organizationId,
    user_id: payload.isGlobal ? null : userId,
    category: payload.category,
    key: payload.key,
    value: payload.value,
    is_global: payload.isGlobal || false,
  };

  const { data, error } = await supabase
    .from('settings')
    .upsert(settingData, {
      onConflict: 'organization_id,user_id,category,key'
    })
    .select()
    .single();

  if (error) {
    throw new SettingsQueryError(`Failed to update setting: ${error.message}`, error.code);
  }

  return {
    id: data.id,
    organizationId: data.organization_id,
    userId: nullToUndefined(data.user_id),
    category: data.category as SettingCategory,
    key: data.key,
    value: data.value,
    isGlobal: data.is_global,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

/**
 * Update multiple settings at once
 */
export async function updateMultipleSettings(
  userId: string,
  organizationId: string,
  payloads: SettingsUpdatePayload[]
): Promise<Setting[]> {
  const supabase = createClient();
  
  const settingsData = payloads.map(payload => ({
    organization_id: organizationId,
    user_id: payload.isGlobal ? null : userId,
    category: payload.category,
    key: payload.key,
    value: payload.value,
    is_global: payload.isGlobal || false,
  }));

  const { data, error } = await supabase
    .from('settings')
    .upsert(settingsData, {
      onConflict: 'organization_id,user_id,category,key'
    })
    .select();

  if (error) {
    throw new SettingsQueryError(`Failed to update settings: ${error.message}`, error.code);
  }

  return (data || []).map(setting => ({
    id: setting.id,
    organizationId: setting.organization_id,
    userId: nullToUndefined(setting.user_id),
    category: setting.category as SettingCategory,
    key: setting.key,
    value: setting.value,
    isGlobal: setting.is_global,
    createdAt: setting.created_at,
    updatedAt: setting.updated_at,
  }));
}

/**
 * Delete a setting
 */
export async function deleteSetting(
  userId: string,
  organizationId: string,
  category: SettingCategory,
  key: string,
  isGlobal = false
): Promise<void> {
  const supabase = createClient();
  
  let query = supabase
    .from('settings')
    .delete()
    .eq('organization_id', organizationId)
    .eq('category', category)
    .eq('key', key);

  if (isGlobal) {
    query = query.is('user_id', null).eq('is_global', true);
  } else {
    query = query.eq('user_id', userId);
  }

  const { error } = await query;

  if (error) {
    throw new SettingsQueryError(`Failed to delete setting: ${error.message}`, error.code);
  }
}

/**
 * Reset user settings to organization defaults
 */
export async function resetToDefaults(
  userId: string,
  organizationId: string,
  category?: SettingCategory
): Promise<void> {
  const supabase = createClient();
  
  let query = supabase
    .from('settings')
    .delete()
    .eq('organization_id', organizationId)
    .eq('user_id', userId);

  if (category) {
    query = query.eq('category', category);
  }

  const { error } = await query;

  if (error) {
    throw new SettingsQueryError(`Failed to reset settings: ${error.message}`, error.code);
  }
}

/**
 * Get default settings for initialization
 */
export function getDefaultUserSettings() {
  return {
    account: {
      profile: {
        displayName: '',
        email: '',
        timezone: 'UTC',
        language: 'en',
      },
      preferences: {
        dateFormat: 'MM/dd/yyyy',
        timeFormat: '12h',
        currency: 'USD',
        numberFormat: 'en-US',
      },
    },
    notifications: {
      email: {
        enabled: true,
        frequency: 'immediate' as const,
        types: {
          orderUpdates: true,
          systemAlerts: true,
          marketing: false,
          security: true,
          teamInvitations: true,
          roleChanges: true,
        },
      },
      push: {
        enabled: false,
        types: {
          orderUpdates: true,
          systemAlerts: true,
          teamNotifications: true,
        },
      },
      sms: {
        enabled: false,
        types: {
          criticalAlerts: true,
          orderUpdates: false,
        },
      },
    },
    privacy: {
      profileVisibility: 'organization' as const,
      dataSharing: {
        analytics: true,
        marketing: false,
        thirdParty: false,
      },
      sessionSettings: {
        rememberMe: true,
        sessionTimeout: 480,
        logoutOnClose: false,
      },
    },
    appearance: {
      theme: 'system' as const,
      density: 'comfortable' as const,
      sidebar: {
        collapsed: false,
        position: 'left' as const,
        pinned: true,
      },
      animations: {
        enabled: true,
        reducedMotion: false,
      },
      customization: {
        accentColor: '#3b82f6',
        fontFamily: 'Inter',
        fontSize: 14,
      },
    },
    security: {
      twoFactor: {
        enabled: false,
        method: 'app' as const,
        backupCodes: [],
      },
      sessions: {
        maxActiveSessions: 5,
        requireReauth: false,
        logSuspiciousActivity: true,
      },
      apiKeys: {
        enabled: false,
        keys: [],
      },
      auditLog: {
        enabled: true,
        retentionDays: 90,
        events: ['login', 'logout', 'password_change', 'role_change'],
      },
    },
  };
} 