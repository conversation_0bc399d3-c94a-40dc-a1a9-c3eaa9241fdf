import { Suspense } from "react"
import { requireAuth } from "@/lib/auth"

export default async function OnboardingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require authentication for onboarding
  await requireAuth()

  return (
    <div className="min-h-screen bg-gray-50">
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
        </div>
      }>
        {children}
      </Suspense>
    </div>
  )
} 