# Netlify Multi-Tenant Implementation Plan

## Overview
This document outlines the changes needed to support multi-tenant functionality when deployed to Netlify.

## Current Issues
- Host detection only handles localhost and standard domain patterns
- Netlify domains follow pattern: `site-name.netlify.app`
- Test pages need updates for Netlify domain patterns

## Implementation Plan

### 1. Update Tenant Detection
Modify `lib/tenant/detection.ts` to:
```typescript
export function extractSubdomain(host: string | null): string | null {
  if (!host) return null;
  
  // Remove port if present
  const hostname = host.split(':')[0];
  
  // Split by dots and check if we have a subdomain
  const parts = hostname.split('.');
  
  // For development (localhost)
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    if (parts.length >= 2) {
      const subdomain = parts[0];
      if (!['www', 'api', 'admin'].includes(subdomain)) {
        return subdomain;
      }
    }
    return null;
  }
  
  // For Netlify domains (site-name.netlify.app)
  if (hostname.endsWith('netlify.app')) {
    const siteName = hostname.replace('.netlify.app', '');
    if (siteName.includes('-')) {
      const [org] = siteName.split('-');
      if (!['www', 'api', 'admin'].includes(org)) {
        return org;
      }
    }
    return null;
  }
  
  // For production domains (subdomain.domain.com)
  if (parts.length >= 3) {
    const subdomain = parts[0];
    if (!['www', 'api', 'admin'].includes(subdomain)) {
      return subdomain;
    }
  }
  
  return null;
}
```

### 2. Update Test Pages

#### Test Tenant Page Updates (`app/test-tenant/page.tsx`):
- Add Netlify domain pattern detection
- Update testing instructions to include Netlify URLs
- Show full domain pattern analysis

#### Multi-Tenant Settings Page Updates (`app/dashboard/organization/multi-tenant/page.tsx`):
- Add Netlify domain examples in testing instructions
- Update domain configuration section to show Netlify URL

### 3. Testing Instructions

#### Local Development
```
http://restaurant1.localhost:3001/test-tenant
```

#### Netlify Production
```
http://restaurant1-site-name.netlify.app/test-tenant
```

#### Custom Domain (When Configured)
```
http://restaurant1.yourdomain.com/test-tenant
```

## Implementation Steps

1. Switch to Code mode to implement tenant detection changes
2. Update test pages with new domain patterns
3. Test functionality:
   - Local development URLs
   - Netlify preview URLs
   - Production Netlify URLs
   - Custom domains (if configured)

## Security Considerations

- Validate subdomain extraction carefully
- Maintain proper tenant isolation across domains
- Ensure custom domain verification process works with Netlify