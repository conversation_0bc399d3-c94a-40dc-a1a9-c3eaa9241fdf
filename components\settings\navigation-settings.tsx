'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { SettingCategory } from '@/types/settings'
import {
  Monitor,
  Zap,
  Sparkles,
  Gem,
  Crown,
  Lightbulb,
  Layers,
  Cpu,
  Wand2,
  RotateCcw,
  Eye,
  Settings,
  Smartphone,
  Tablet
} from 'lucide-react'

interface NavigationSettingsProps {
  getSetting: (category: SettingCategory, key: string) => any
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>
  onReset: () => void
}

const basicVariants = [
  {
    key: 'default',
    name: 'Classic White',
    description: 'Clean and professional white sidebar',
    icon: Monitor,
    color: 'bg-white border',
    category: 'basic'
  },
  {
    key: 'modern',
    name: 'Dark Modern',
    description: 'Sleek dark theme for modern apps',
    icon: Zap,
    color: 'bg-slate-900',
    category: 'basic'
  },
  {
    key: 'minimal',
    name: 'Minimal Gray',
    description: 'Subtle gray theme that stays minimal',
    icon: Sparkles,
    color: 'bg-gray-50 border',
    category: 'basic'
  },
  {
    key: 'glass',
    name: 'Glass Morphism',
    description: 'Modern glass effect with backdrop blur',
    icon: Gem,
    color: 'bg-white/80 backdrop-blur border',
    category: 'basic'
  },
  {
    key: 'gradient',
    name: 'Brand Gradient',
    description: 'Beautiful gradient with brand colors',
    icon: Crown,
    color: 'bg-gradient-to-b from-orange-500 to-red-600',
    category: 'basic'
  }
]

const advancedVariants = [
  {
    key: 'neon',
    name: 'Neon Cyber',
    description: 'Futuristic neon theme with glowing effects',
    icon: Lightbulb,
    color: 'bg-black border border-cyan-500',
    category: 'advanced'
  },
  {
    key: 'floating',
    name: 'Floating Card',
    description: 'Elevated floating design with rounded corners',
    icon: Layers,
    color: 'bg-white/95 backdrop-blur border shadow-lg',
    category: 'advanced'
  },
  {
    key: 'compact',
    name: 'Compact Pro',
    description: 'Space-efficient design for power users',
    icon: Cpu,
    color: 'bg-slate-800',
    category: 'advanced'
  },
  {
    key: 'premium',
    name: 'Premium Suite',
    description: 'Feature-rich premium experience',
    icon: Crown,
    color: 'bg-gradient-to-b from-slate-900 to-slate-800',
    category: 'advanced'
  },
  {
    key: 'animated',
    name: 'Animated Magic',
    description: 'Smooth animations and micro-interactions',
    icon: Wand2,
    color: 'bg-white border',
    category: 'advanced'
  }
]

export function NavigationSettings({
  getSetting,
  updateSetting,
  onReset,
}: NavigationSettingsProps) {
  const [selectedCategory, setSelectedCategory] = useState<'basic' | 'advanced'>('basic')
  
  // Get current settings with defaults
  const navigation = getSetting('navigation', 'settings') || {
    variant: 'premium',
    style: 'premium',
    collapsed: false,
    position: 'left',
    showLabels: true,
    showBadges: true,
    compactMode: false,
    animations: true,
    autoCollapse: false,
    mobileOverlay: true,
  }

  const allVariants = [...basicVariants, ...advancedVariants]
  const currentVariants = selectedCategory === 'basic' ? basicVariants : advancedVariants

  const updateNavigation = async (field: string, value: any) => {
    const newSettings = {
      ...navigation,
      [field]: value,
    }

    // Save to localStorage for immediate effect
    if (typeof window !== 'undefined') {
      localStorage.setItem('navigation-settings', JSON.stringify(newSettings))
    }

    // Also save to database
    await updateSetting('navigation', 'settings', newSettings)
  }

  const handleVariantSelect = async (variant: string) => {
    const selectedVariant = allVariants.find(v => v.key === variant)
    await updateNavigation('variant', variant)
    await updateNavigation('style', selectedVariant?.category === 'advanced' ? 'premium' : 'standard')

    // Trigger a page reload to apply the new sidebar
    window.location.reload()
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Navigation Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Customize your sidebar navigation appearance and behavior.
        </p>
      </div>

      <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="flex items-center space-x-2">
            <Monitor className="h-4 w-4" />
            <span>Basic Styles</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center space-x-2">
            <Crown className="h-4 w-4" />
            <span>Premium Styles</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Basic Collection</CardTitle>
              <CardDescription>
                Essential sidebar designs for everyday use
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {basicVariants.map((variant) => {
                  const Icon = variant.icon
                  const isSelected = navigation.variant === variant.key
                  
                  return (
                    <Card 
                      key={variant.key}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        isSelected ? 'ring-2 ring-orange-500 shadow-md' : ''
                      }`}
                      onClick={() => handleVariantSelect(variant.key)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className={`h-10 w-10 rounded-lg ${variant.color} flex items-center justify-center`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm font-medium truncate">{variant.name}</p>
                              {isSelected && (
                                <Badge variant="default" className="text-xs">
                                  Active
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground truncate">
                              {variant.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center space-x-2">
                <Crown className="h-5 w-5 text-orange-500" />
                <span>Premium Collection</span>
              </CardTitle>
              <CardDescription>
                Advanced sidebar designs with premium features and effects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {advancedVariants.map((variant) => {
                  const Icon = variant.icon
                  const isSelected = navigation.variant === variant.key
                  
                  return (
                    <Card 
                      key={variant.key}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        isSelected ? 'ring-2 ring-orange-500 shadow-md' : ''
                      }`}
                      onClick={() => handleVariantSelect(variant.key)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className={`h-10 w-10 rounded-lg ${variant.color} flex items-center justify-center`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm font-medium truncate">{variant.name}</p>
                              {isSelected && (
                                <Badge variant="default" className="text-xs">
                                  Active
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground truncate">
                              {variant.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Separator />

      {/* Navigation Behavior Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Behavior Settings</span>
          </CardTitle>
          <CardDescription>
            Configure how your navigation behaves
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="collapsed">Start Collapsed</Label>
                  <p className="text-xs text-muted-foreground">
                    Sidebar starts in collapsed state
                  </p>
                </div>
                <Switch
                  id="collapsed"
                  checked={navigation.collapsed}
                  onCheckedChange={(checked) => updateNavigation('collapsed', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="showLabels">Show Labels</Label>
                  <p className="text-xs text-muted-foreground">
                    Display text labels for navigation items
                  </p>
                </div>
                <Switch
                  id="showLabels"
                  checked={navigation.showLabels}
                  onCheckedChange={(checked) => updateNavigation('showLabels', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="showBadges">Show Badges</Label>
                  <p className="text-xs text-muted-foreground">
                    Display notification badges
                  </p>
                </div>
                <Switch
                  id="showBadges"
                  checked={navigation.showBadges}
                  onCheckedChange={(checked) => updateNavigation('showBadges', checked)}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="animations">Enable Animations</Label>
                  <p className="text-xs text-muted-foreground">
                    Smooth transitions and effects
                  </p>
                </div>
                <Switch
                  id="animations"
                  checked={navigation.animations}
                  onCheckedChange={(checked) => updateNavigation('animations', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="autoCollapse">Auto Collapse</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically collapse on mobile
                  </p>
                </div>
                <Switch
                  id="autoCollapse"
                  checked={navigation.autoCollapse}
                  onCheckedChange={(checked) => updateNavigation('autoCollapse', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="mobileOverlay">Mobile Overlay</Label>
                  <p className="text-xs text-muted-foreground">
                    Show overlay on mobile devices
                  </p>
                </div>
                <Switch
                  id="mobileOverlay"
                  checked={navigation.mobileOverlay}
                  onCheckedChange={(checked) => updateNavigation('mobileOverlay', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reset Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base text-destructive">Reset Navigation Settings</CardTitle>
          <CardDescription>
            Reset all navigation preferences to their default values
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button variant="outline" onClick={onReset} className="w-full sm:w-auto">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
