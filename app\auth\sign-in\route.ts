import { createClient } from '@/lib/supabase/server'
import { createUserProfile } from '@/lib/auth'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  remember: z.boolean().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const result = signInSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.errors },
        { status: 400 }
      )
    }

    const { email, password, remember } = result.data
    const supabase = createClient()

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (authError) {
      console.error('Auth signin error:', authError)
      
      // Provide user-friendly error messages
      let errorMessage = 'Invalid email or password'
      
      if (authError.message.includes('Email not confirmed')) {
        errorMessage = 'Please check your email and click the confirmation link before signing in'
      } else if (authError.message.includes('Invalid login credentials')) {
        errorMessage = 'Invalid email or password'
      } else if (authError.message.includes('Too many requests')) {
        errorMessage = 'Too many login attempts. Please try again later'
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: 400 }
      )
    }

    if (!authData.user || !authData.session) {
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      )
    }

    // Ensure user profile exists in database
    try {
      await createUserProfile(authData.user)
    } catch (profileError) {
      console.error('Error ensuring user profile:', profileError)
      // Continue with login even if profile creation fails
    }

    // Handle remember me functionality
    if (remember) {
      // Extend session duration (Supabase handles this automatically)
      // The session will persist based on Supabase configuration
    }

    // Get redirect URL from query params or default to dashboard
    const url = new URL(request.url)
    const redirectTo = url.searchParams.get('redirectTo') || '/dashboard/overview'

    // Return success response with user data
    return NextResponse.json({
      message: 'Signed in successfully',
      user: {
        id: authData.user.id,
        email: authData.user.email,
        emailConfirmed: !!authData.user.email_confirmed_at,
        fullName: authData.user.user_metadata?.full_name,
      },
      session: authData.session,
      redirectTo,
    })

  } catch (error) {
    console.error('Signin error:', error)
    return NextResponse.json(
      { error: 'Internal server error during signin' },
      { status: 500 }
    )
  }
} 