'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Sidebar, 
  SidebarProvider, 
  MobileSidebarTrigger 
} from '@/components/layout/sidebar-navigation'
import {
  Palette,
  Eye,
  Smartphone,
  Monitor,
  Tablet,
  Star,
  Zap,
  Sparkles,
  Crown,
  Gem,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'

const basicVariants = [
  {
    key: 'default',
    name: 'Classic White',
    description: 'Clean and professional white sidebar with subtle shadows',
    icon: Monitor,
    color: 'bg-white',
    features: ['Clean Design', 'Professional Look', 'High Contrast', 'Accessibility Friendly'],
    recommended: true
  },
  {
    key: 'modern',
    name: 'Dark Modern',
    description: 'Sleek dark theme perfect for modern applications',
    icon: Zap,
    color: 'bg-slate-900',
    features: ['Dark Theme', 'Modern Look', 'Eye-friendly', 'Premium Feel'],
    recommended: false
  },
  {
    key: 'minimal',
    name: 'Minimal Gray',
    description: 'Subtle gray theme that stays out of the way',
    icon: Sparkles,
    color: 'bg-gray-50',
    features: ['Minimal Design', 'Subtle Colors', 'Focus on Content', 'Clean Lines'],
    recommended: false
  },
  {
    key: 'glass',
    name: 'Glass Morphism',
    description: 'Modern glass effect with backdrop blur',
    icon: Gem,
    color: 'bg-white/80',
    features: ['Glass Effect', 'Backdrop Blur', 'Modern Aesthetic', 'Translucent'],
    recommended: false
  },
  {
    key: 'gradient',
    name: 'Brand Gradient',
    description: 'Beautiful gradient matching your brand colors',
    icon: Crown,
    color: 'bg-gradient-to-b from-orange-500 to-red-600',
    features: ['Brand Colors', 'Eye-catching', 'Unique Look', 'Memorable'],
    recommended: false
  }
]

export default function BasicSidebarCollectionPage() {
  const [selectedVariant, setSelectedVariant] = useState('default')
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  const viewModeClasses = {
    desktop: 'w-full max-w-7xl',
    tablet: 'w-full max-w-4xl',
    mobile: 'w-full max-w-sm'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <Link href="/sidebar-demo">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Gallery
            </Button>
          </Link>
          
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <Palette className="h-8 w-8 text-orange-600" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                Basic Collection
              </h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Essential sidebar designs perfect for everyday use. These variants provide clean, professional looks 
              that work well in any business application.
            </p>
          </div>
        </div>

        {/* Variant Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {basicVariants.map((variant) => {
            const Icon = variant.icon
            const isSelected = selectedVariant === variant.key
            
            return (
              <Card 
                key={variant.key}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  isSelected ? 'ring-2 ring-orange-500 shadow-lg' : ''
                }`}
                onClick={() => setSelectedVariant(variant.key)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`h-8 w-8 rounded-lg ${variant.color} flex items-center justify-center border`}>
                        <Icon className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-sm">{variant.name}</CardTitle>
                        {isSelected && (
                          <Badge variant="default" className="text-xs mt-1">
                            Active
                          </Badge>
                        )}
                      </div>
                    </div>
                    {variant.recommended && (
                      <Badge variant="secondary" className="text-xs">
                        Recommended
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-xs mb-3">
                    {variant.description}
                  </CardDescription>
                  <div className="space-y-1">
                    {variant.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-1">
                        <Star className="h-3 w-3 text-orange-500" />
                        <span className="text-xs text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* View Mode Controls */}
        <div className="flex items-center justify-center space-x-4">
          <span className="text-sm font-medium text-gray-700">View Mode:</span>
          <div className="flex items-center space-x-2">
            {[
              { key: 'desktop', icon: Monitor, label: 'Desktop' },
              { key: 'tablet', icon: Tablet, label: 'Tablet' },
              { key: 'mobile', icon: Smartphone, label: 'Mobile' }
            ].map((mode) => {
              const Icon = mode.icon
              return (
                <Button
                  key={mode.key}
                  variant={viewMode === mode.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode(mode.key as any)}
                  className="flex items-center space-x-1"
                >
                  <Icon className="h-4 w-4" />
                  <span>{mode.label}</span>
                </Button>
              )
            })}
          </div>
        </div>

        {/* Live Preview */}
        <Card className="overflow-hidden">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Live Preview</span>
                </CardTitle>
                <CardDescription>
                  Interactive preview of the {basicVariants.find(v => v.key === selectedVariant)?.name} sidebar
                </CardDescription>
              </div>
              <Badge variant="outline" className="text-xs">
                {selectedVariant}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="bg-gray-100 p-4 flex justify-center">
              <div className={`${viewModeClasses[viewMode]} bg-white rounded-lg shadow-lg overflow-hidden`}>
                <SidebarProvider>
                  <div className="flex h-96">
                    <Sidebar variant={selectedVariant as any} />
                    <div className="flex-1 p-6 bg-gray-50">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
                          <p className="text-gray-600">Welcome to your restaurant dashboard</p>
                        </div>
                        <MobileSidebarTrigger />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Sample Content</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-gray-600">
                              This is how your content would look alongside the sidebar.
                              The sidebar is fully responsive and collapsible.
                            </p>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Features</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="text-sm text-gray-600 space-y-1">
                              <li>• Fully responsive design</li>
                              <li>• Collapsible on desktop</li>
                              <li>• Mobile-friendly overlay</li>
                              <li>• Smooth animations</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </SidebarProvider>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Code */}
        <Card>
          <CardHeader>
            <CardTitle>Implementation Code</CardTitle>
            <CardDescription>
              Copy this code to use the {basicVariants.find(v => v.key === selectedVariant)?.name} sidebar in your project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm">
                <code>{`import { SidebarProvider, Sidebar, MobileSidebarTrigger } from '@/components/layout/sidebar-navigation'

export default function Layout({ children }) {
  return (
    <SidebarProvider>
      <div className="flex h-screen">
        <Sidebar variant="${selectedVariant}" />
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-white shadow-sm border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-xl font-semibold">Dashboard</h1>
              <MobileSidebarTrigger />
            </div>
          </header>
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}`}</code>
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
