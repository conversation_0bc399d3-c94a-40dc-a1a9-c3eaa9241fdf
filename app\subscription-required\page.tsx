import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface SubscriptionRequiredPageProps {
  searchParams: {
    error?: string;
  };
}

export default function SubscriptionRequiredPage({ searchParams }: SubscriptionRequiredPageProps) {
  const error = searchParams.error || 'Subscription required';

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="rounded-full bg-red-100 p-3">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Subscription Required
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {error}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                What happened?
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>Your organization&apos;s subscription is currently inactive.</p>
                <p>This could be because:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Your trial period has expired</li>
                  <li>Your subscription payment is past due</li>
                  <li>Your subscription has been cancelled</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <Button
                asChild
                className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
              >
                <Link href="/billing">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Manage Subscription
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                className="w-full"
              >
                <Link href="/dashboard">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Link>
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                Need help? Contact our{' '}
                <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-500">
                  support team
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 