# 🚀 Restaurant SaaS Setup Guide

This guide will help you complete the Supabase setup and get your restaurant management platform running.

## 📋 Current Status

✅ **Completed:**

- Project structure and dependencies
- Supabase client configuration (browser & server)
- TypeScript type definitions
- Database query utilities
- Test page for connection verification
- Environment configuration template
- **FIXED**: Database schema with proper RLS policies

⚠️ **Needs Setup:**

- Supabase project creation
- Database schema deployment
- Environment variables configuration

## 🎯 Quick Setup (15 minutes)

### Step 1: Create Supabase Project

1. Go to [supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign up or log in
3. Click **"New Project"**
4. Choose your organization (or create one)
5. Project settings:
   - **Name:** `restaurant-saas`
   - **Database Password:** Generate a strong password (save it!)
   - **Region:** Choose closest to your location
6. Click **"Create new project"**
7. **Wait 2-3 minutes** for the project to be ready

### Step 2: Get Your API Credentials

1. In your Supabase dashboard, go to **Project Settings** → **API**
2. Copy these values:
   - **Project URL** (starts with `https://`)
   - **anon public** key (starts with `eyJ`)
   - **service_role** key (starts with `eyJ`)

### Step 3: Configure Environment Variables

1. Open your `.env.local` file
2. Replace the placeholder values:

```env
# Replace these with your actual Supabase credentials
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Keep these as-is for now
NEXT_PUBLIC_APP_URL=http://localhost:3002
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
```

### Step 4: Set Up Database Schema

🚨 **Use the FIXED version to avoid errors:**

1. In Supabase dashboard, go to **SQL Editor**
2. Copy the entire contents of `docs/database-schema-fixed.sql` ⭐ **(Use this one!)**
3. Paste it into the SQL Editor
4. Click **"Run"** to execute the script
5. You should see: `Restaurant SaaS database schema setup complete - FIXED VERSION!`

### Step 5: Test Your Setup

1. Restart your development server:

   ```bash
   npm run dev
   ```

2. Visit: [http://localhost:3002/test-db](http://localhost:3002/test-db) (note: port might be 3002)

3. You should see:
   - ✅ "Connection Successful"
   - Sample organization data
   - All tables showing as accessible

## 🔧 What Was Fixed

The original schema had issues with:

- **RLS Policy Order**: Policies were created before tables were fully ready
- **Column References**: `auth_user_id` column wasn't properly available when policies referenced it
- **Circular Dependencies**: Some policies created infinite recursion

The fixed version:

- ✅ Creates tables first, then indexes, then triggers
- ✅ Inserts seed data before enabling RLS
- ✅ Creates service role policies first for admin access
- ✅ Uses `EXISTS` clauses instead of `IN` subqueries for better performance
- ✅ Proper order of operations to avoid column reference errors

## 🎉 What You Get

After setup, your platform includes:

### 🏢 **Multi-Tenant Architecture**

- Organizations (restaurant companies)
- Users with role-based access
- Secure data isolation

### 🍽️ **Menu Management**

- Dynamic menu creation
- Categories and items
- Pricing and availability
- Public menu publishing

### 💳 **Billing Ready**

- Stripe integration structure
- Subscription management
- Invoice tracking

### 🔒 **Security First**

- Row Level Security (RLS)
- Service role for admin operations
- Multi-tenant data protection

## 🔧 Troubleshooting

### Connection Failed?

1. **Check Environment Variables**

   - Ensure no extra spaces
   - Verify URLs start with `https://`
   - Confirm keys start with `eyJ`

2. **Restart Development Server**

   ```bash
   # Stop the server (Ctrl+C)
   npm run dev
   ```

3. **Check Supabase Project Status**
   - Project should be "Active" in dashboard
   - Database should be running

### Database Schema Issues?

1. **Use the Fixed Schema**

   - Always use `docs/database-schema-fixed.sql`
   - This version resolves all column reference issues

2. **Re-run the Schema**

   - Go to SQL Editor in Supabase
   - Run `docs/database-schema-fixed.sql` again
   - Check for any error messages

3. **Check Table Creation**
   - Go to **Table Editor** in Supabase
   - Verify tables were created:
     - organizations
     - users
     - menus
     - menu_categories
     - menu_items

### Still Having Issues?

1. **Check Browser Console**

   - Open Developer Tools (F12)
   - Look for error messages
   - Check Network tab for failed requests

2. **Check Server Logs**
   - Look at your terminal where `npm run dev` is running
   - Check for error messages

## 🎯 Next Steps After Setup

Once your connection test shows success:

1. **Explore the Platform**

   - Main landing page: [http://localhost:3002](http://localhost:3002)
   - Database test: [http://localhost:3002/test-db](http://localhost:3002/test-db)

2. **Development Workflow**

   - Add new features to the restaurant platform
   - Use the generated TypeScript types
   - Leverage the database query utilities

3. **Authentication Setup** (Future)

   - Configure Supabase Auth
   - Set up user registration/login
   - Implement role-based access

4. **Stripe Integration** (Future)
   - Set up Stripe account
   - Configure webhook endpoints
   - Implement subscription billing

## 📁 Important Files

- **Environment:** `.env.local` - Your secret configuration
- **Database Schema:** `docs/database-schema-fixed.sql` - ⭐ **Use this one!**
- **Types:** `types/database.ts` - TypeScript definitions
- **Queries:** `lib/database/queries.ts` - Database operations
- **Supabase Clients:** `lib/supabase/` - Connection management

## 🆘 Need Help?

Your setup should work after following these steps. The test page at `/test-db` will guide you through any remaining issues with clear error messages and instructions.

**Pro Tip:** The landing page (first screenshot) is your main product page. The database test page (second screenshot) is for development verification only.
