'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { useSettings } from '@/hooks/use-settings';
import { Setting } from '@/types/settings';
import { 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Settings as SettingsIcon, 
  Building, 
  Plug,
  Save,
  RotateCcw,
  AlertTriangle
} from 'lucide-react';

// Import setting panels
import { AccountSettings } from './account-settings';
import { NotificationSettings } from './notification-settings';
import { PrivacySettings } from './privacy-settings';
import { AppearanceSettings } from './appearance-settings';
import { SecuritySettings } from './security-settings';
import { OrganizationSettings } from './organization-settings';
import { IntegrationSettings } from './integration-settings';

interface SettingsPageClientProps {
  organizationId: string;
  userId: string;
  userRole: string;
  initialSettings: Setting[];
  userEmail: string;
  userName: string;
  organizationName: string;
}

export function SettingsPageClient({
  organizationId,
  userRole,
  userEmail,
  userName,
  organizationName,
}: SettingsPageClientProps) {
  const [activeTab, setActiveTab] = useState('account');
  
  const {
    settings,
    loading,
    error,
    updateSetting,
    getSetting,
    resetSettings,
    hasUnsavedChanges,
    saveChanges,
  } = useSettings({
    organizationId,
    autoSave: false, // Manual save for better UX
  });

  const tabs = [
    {
      id: 'account',
      label: 'Account',
      icon: User,
      description: 'Personal information and preferences',
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Email, push, and SMS notification settings',
    },
    {
      id: 'privacy',
      label: 'Privacy',
      icon: Shield,
      description: 'Data sharing and session preferences',
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: Palette,
      description: 'Theme, layout, and customization options',
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Two-factor auth, sessions, and API keys',
    },
    {
      id: 'integrations',
      label: 'Integrations',
      icon: Plug,
      description: 'Third-party services and webhooks',
    },
    ...(userRole === 'owner' || userRole === 'admin' ? [{
      id: 'organization',
      label: 'Organization',
      icon: Building,
      description: 'Organization-wide settings and policies',
    }] : []),
  ];

  const handleResetCategory = async (category: string) => {
    if (confirm(`Are you sure you want to reset all ${category} settings to defaults?`)) {
      await resetSettings(category as any);
    }
  };

  if (loading && settings.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <SettingsIcon className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Error Loading Settings
          </CardTitle>
          <CardDescription>
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Save/Reset Actions */}
      {hasUnsavedChanges && (
        <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                <span className="font-medium">You have unsaved changes</span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Discard
                </Button>
                <Button
                  size="sm"
                  onClick={saveChanges}
                  disabled={loading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="border-b">
          <TabsList className="grid w-full grid-cols-6 lg:grid-cols-7 h-auto p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center gap-1 py-3 px-2 text-xs"
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          <TabsContent value="account" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Account Settings</h2>
              <p className="text-muted-foreground">
                Manage your personal information and account preferences.
              </p>
            </div>
            <AccountSettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              userEmail={userEmail}
              userName={userName}
              onReset={() => handleResetCategory('account')}
            />
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Notification Settings</h2>
              <p className="text-muted-foreground">
                Configure how and when you receive notifications.
              </p>
            </div>
            <NotificationSettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              onReset={() => handleResetCategory('notifications')}
            />
          </TabsContent>

          <TabsContent value="privacy" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Privacy Settings</h2>
              <p className="text-muted-foreground">
                Control your data sharing and privacy preferences.
              </p>
            </div>
            <PrivacySettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              onReset={() => handleResetCategory('privacy')}
            />
          </TabsContent>

          <TabsContent value="appearance" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Appearance Settings</h2>
              <p className="text-muted-foreground">
                Customize the look and feel of your interface.
              </p>
            </div>
            <AppearanceSettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              onReset={() => handleResetCategory('appearance')}
            />
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Security Settings</h2>
              <p className="text-muted-foreground">
                Manage your account security and authentication.
              </p>
            </div>
            <SecuritySettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              onReset={() => handleResetCategory('security')}
            />
          </TabsContent>

          <TabsContent value="integrations" className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Integration Settings</h2>
              <p className="text-muted-foreground">
                Connect and configure third-party services.
              </p>
            </div>
            <IntegrationSettings
              getSetting={getSetting}
              updateSetting={updateSetting}
              onReset={() => handleResetCategory('integrations')}
            />
          </TabsContent>

          {(userRole === 'owner' || userRole === 'admin') && (
            <TabsContent value="organization" className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h2 className="text-2xl font-semibold">Organization Settings</h2>
                  <Badge variant="secondary">Admin Only</Badge>
                </div>
                <p className="text-muted-foreground">
                  Configure organization-wide settings and policies.
                </p>
              </div>
              <OrganizationSettings
                getSetting={getSetting}
                updateSetting={updateSetting}
                organizationName={organizationName}
                onReset={() => handleResetCategory('organization')}
              />
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
} 