'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Permission, PERMISSIONS } from '@/types/roles';
import { useRoles } from '@/lib/contexts/role-context';

interface CreateRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateRoleDialog({ open, onOpenChange }: CreateRoleDialogProps) {
  const { createRole } = useRoles();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<Permission[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePermissionChange = (permission: Permission, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permission]);
    } else {
      setSelectedPermissions(prev => 
        prev.filter(p => 
          !(p.resource === permission.resource && p.action === permission.action)
        )
      );
    }
  };

  const isPermissionSelected = (permission: Permission) => {
    return selectedPermissions.some(p => 
      p.resource === permission.resource && p.action === permission.action
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || selectedPermissions.length === 0) return;

    setIsSubmitting(true);
    try {
      await createRole({
        name: name.trim(),
        description: description.trim() || undefined,
        permissions: selectedPermissions,
      });
      
      setName('');
      setDescription('');
      setSelectedPermissions([]);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to create role:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Group permissions by resource
  const permissionGroups = Object.entries(PERMISSIONS).reduce((groups, [key, permission]) => {
    const resource = permission.resource;
    if (!groups[resource]) {
      groups[resource] = [];
    }
    groups[resource].push({ key, permission });
    return groups;
  }, {} as Record<string, Array<{ key: string; permission: Permission }>>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Role</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Role Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Content Manager"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what this role can do..."
              />
            </div>
          </div>

          <div>
            <Label className="text-base font-medium">Permissions</Label>
            <p className="text-sm text-muted-foreground mb-4">
              Select the permissions this role should have
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(permissionGroups).map(([resource, permissions]) => (
                <div key={resource} className="space-y-3">
                  <h4 className="font-medium capitalize text-sm">
                    {resource.replace('_', ' ')} Management
                  </h4>
                  <div className="space-y-2 pl-4">
                    {permissions.map(({ key, permission }) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Checkbox
                          id={key}
                          checked={isPermissionSelected(permission)}
                          onCheckedChange={(checked) => 
                            handlePermissionChange(permission, checked as boolean)
                          }
                        />
                        <Label 
                          htmlFor={key} 
                          className="text-sm font-normal cursor-pointer"
                        >
                          {permission.action.charAt(0).toUpperCase() + permission.action.slice(1)} {resource}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-between items-center pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              {selectedPermissions.length} permission{selectedPermissions.length !== 1 ? 's' : ''} selected
            </p>
            <div className="flex gap-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || !name.trim() || selectedPermissions.length === 0}
              >
                {isSubmitting ? 'Creating...' : 'Create Role'}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 