import { headers } from 'next/headers';
import { getTenantContext } from '@/lib/tenant/auth';
import { detectTenant } from '@/lib/tenant/detection';
import { validateTenant } from '@/lib/tenant/validation';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import type { TenantDetectionResult, TenantRequestContext } from '@/types/tenant';

export default async function TenantTestPage() {
  const headersList = headers();
  const host = headersList.get('host');
  
  // Get current session
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value;
        }
      }
    }
  );

  const { data: { user } } = await supabase.auth.getUser();
  const session = user ? { user } : null;
  
  // Test tenant detection
  let tenantDetection: TenantDetectionResult | null = null;
  let tenantValidation: { isValid: boolean; error?: string } | null = null;
  let tenantContext: TenantRequestContext | null = null;
  let userOrganizations: any[] = [];

  try {
    tenantDetection = await detectTenant(host);
    
    if (tenantDetection.organization) {
      tenantValidation = await validateTenant(tenantDetection.organization);
    }
    
    if (session) {
      tenantContext = await getTenantContext();
      
      // Get user's organizations
      const { data: organizations } = await supabase
        .from('organizations')
        .select('*')
        .order('created_at', { ascending: false });
      
      userOrganizations = organizations || [];
    }
  } catch (error) {
    console.error('Error in tenant test:', error);
  }

  const StatusIcon = ({ success }: { success: boolean }) => 
    success ? <CheckCircle className="h-5 w-5 text-green-500" /> : <XCircle className="h-5 w-5 text-red-500" />;

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">RS-007 Multi-Tenant Test Page</h1>
        <p className="text-gray-600 mt-2">Testing multi-tenant middleware functionality</p>
      </div>

      {/* Host Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Host Information
          </CardTitle>
          <CardDescription>Current request host and detection status</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="font-semibold">Host:</label>
              <p className="text-sm bg-gray-100 p-2 rounded">{host || 'Not detected'}</p>
            </div>
            <div>
              <label className="font-semibold">Detection Type:</label>
              <Badge variant={tenantDetection?.type === 'none' ? 'secondary' : 'default'}>
                {tenantDetection?.type || 'none'}
              </Badge>
            </div>
          </div>
          {tenantDetection?.value && (
            <div>
              <label className="font-semibold">Detected Value:</label>
              <p className="text-sm bg-gray-100 p-2 rounded">{tenantDetection.value}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tenant Detection Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <StatusIcon success={!!tenantDetection?.organization} />
            Tenant Detection
          </CardTitle>
          <CardDescription>Results from tenant detection system</CardDescription>
        </CardHeader>
        <CardContent>
          {tenantDetection?.organization ? (
            <div className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="font-semibold">Organization ID:</label>
                  <p className="text-sm bg-gray-100 p-2 rounded font-mono">
                    {tenantDetection.organization.organizationId}
                  </p>
                </div>
                <div>
                  <label className="font-semibold">Slug:</label>
                  <p className="text-sm bg-gray-100 p-2 rounded">
                    {tenantDetection.organization.slug}
                  </p>
                </div>
                <div>
                  <label className="font-semibold">Subscription Status:</label>
                  <Badge variant={
                    tenantDetection.organization.subscriptionStatus === 'active' ? 'default' :
                    tenantDetection.organization.subscriptionStatus === 'trial' ? 'secondary' : 'destructive'
                  }>
                    {tenantDetection.organization.subscriptionStatus}
                  </Badge>
                </div>
                <div>
                  <label className="font-semibold">Active:</label>
                  <Badge variant={tenantDetection.organization.isActive ? 'default' : 'destructive'}>
                    {tenantDetection.organization.isActive ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
              
              {tenantDetection.organization.features.length > 0 && (
                <div>
                  <label className="font-semibold">Features:</label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tenantDetection.organization.features.map((feature) => (
                      <Badge key={feature} variant="outline">{feature}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <p className="text-gray-600">No tenant detected from current host</p>
              <p className="text-sm text-gray-500 mt-2 space-y-1">
                <strong>Try accessing via:</strong>
                <br />
                • Local development: <code>restaurant1.localhost:3001</code>
                <br />
                • Netlify: <code>restaurant1-site-name.netlify.app</code>
                <br />
                • Custom domain: <code>restaurant1.yourdomain.com</code>
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tenant Validation */}
      {tenantDetection?.organization && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <StatusIcon success={tenantValidation?.isValid || false} />
              Tenant Validation
            </CardTitle>
            <CardDescription>Subscription and access validation results</CardDescription>
          </CardHeader>
          <CardContent>
            {tenantValidation?.isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span>Tenant validation passed</span>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-red-600">
                  <XCircle className="h-5 w-5" />
                  <span>Tenant validation failed</span>
                </div>
                {tenantValidation?.error && (
                  <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    {tenantValidation.error}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <StatusIcon success={!!session} />
            Authentication Status
          </CardTitle>
          <CardDescription>Current user session and tenant context</CardDescription>
        </CardHeader>
        <CardContent>
          {session ? (
            <div className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="font-semibold">User ID:</label>
                  <p className="text-sm bg-gray-100 p-2 rounded font-mono">{session.user.id}</p>
                </div>
                <div>
                  <label className="font-semibold">Email:</label>
                  <p className="text-sm bg-gray-100 p-2 rounded">{session.user.email}</p>
                </div>
              </div>
              
              {tenantContext && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="font-semibold">Role in Organization:</label>
                    <Badge>{tenantContext.user.role}</Badge>
                  </div>
                  <div>
                    <label className="font-semibold">Permissions:</label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {tenantContext.user.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600">No active session</p>
              <p className="text-sm text-gray-500 mt-1">Please log in to test tenant context</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Organizations */}
      {session && userOrganizations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>User Organizations</CardTitle>
            <CardDescription>Organizations this user has access to</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {userOrganizations.map((org: any) => (
                <div key={org.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">{org.name}</h4>
                      <p className="text-sm text-gray-600">{org.slug}</p>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant={org.subscription_status === 'active' ? 'default' : 'secondary'}>
                        {org.subscription_status}
                      </Badge>
                      {org.id === tenantDetection?.organization?.organizationId && (
                        <Badge variant="outline">Current</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
          <CardDescription>How to test the multi-tenant functionality</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">To test subdomain detection:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
              <li>Add entries to your hosts file: <code>127.0.0.1 restaurant1.localhost</code></li>
              <li>Create an organization with slug &quot;restaurant1&quot; in the database</li>
              <li>Visit: <code>http://restaurant1.localhost:3001/test-tenant</code></li>
              <li>Check if tenant detection shows &quot;subdomain&quot; type</li>
            </ol>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">To test RLS policies:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-green-700">
              <li>Log in as different users</li>
              <li>Create organizations with different users</li>
              <li>Verify users can only see their own organization&apos;s data</li>
              <li>Test menu, category, and item access restrictions</li>
            </ol>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">To test middleware protection:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
              <li>Try accessing /menus, /orders, /analytics routes</li>
              <li>Check if redirected appropriately based on tenant status</li>
              <li>Test with active vs inactive subscriptions</li>
              <li>Verify role-based access control</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 