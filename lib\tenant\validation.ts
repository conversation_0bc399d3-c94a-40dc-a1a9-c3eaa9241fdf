import { TenantInfo, TenantMiddlewareConfig } from '@/types/tenant';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * Validate if tenant exists and is active
 */
export async function validateTenant(tenant: TenantInfo | null): Promise<{
  isValid: boolean;
  error?: string;
}> {
  if (!tenant) {
    return {
      isValid: false,
      error: 'Tenant not found'
    };
  }

  // Check if organization is active
  if (!tenant.isActive) {
    return {
      isValid: false,
      error: 'Organization subscription is not active'
    };
  }

  // Check if trial has expired
  if (tenant.subscriptionStatus === 'trial' && tenant.trialEndsAt && tenant.trialEndsAt < new Date()) {
    return {
      isValid: false,
      error: 'Trial period has expired'
    };
  }

  // Check for other inactive statuses
  if (['cancelled', 'past_due'].includes(tenant.subscriptionStatus)) {
    return {
      isValid: false,
      error: `Subscription is ${tenant.subscriptionStatus}`
    };
  }

  return { isValid: true };
}

/**
 * Check if tenant has required features
 */
export function hasFeature(tenant: TenantInfo, feature: string): boolean {
  return tenant.features.includes(feature);
}

/**
 * Check if tenant has any of the required features
 */
export function hasAnyFeature(tenant: TenantInfo, features: string[]): boolean {
  return features.some(feature => tenant.features.includes(feature));
}

/**
 * Check if tenant has all required features
 */
export function hasAllFeatures(tenant: TenantInfo, features: string[]): boolean {
  return features.every(feature => tenant.features.includes(feature));
}

/**
 * Validate tenant status synchronously (for basic checks)
 */
function validateTenantSync(tenant: TenantInfo | null): {
  isValid: boolean;
  error?: string;
} {
  if (!tenant) {
    return {
      isValid: false,
      error: 'Tenant not found'
    };
  }

  // Check if organization is active
  if (!tenant.isActive) {
    return {
      isValid: false,
      error: 'Organization subscription is not active'
    };
  }

  // Check if trial has expired
  if (tenant.subscriptionStatus === 'trial' && tenant.trialEndsAt && tenant.trialEndsAt < new Date()) {
    return {
      isValid: false,
      error: 'Trial period has expired'
    };
  }

  // Check for other inactive statuses
  if (['cancelled', 'past_due'].includes(tenant.subscriptionStatus)) {
    return {
      isValid: false,
      error: `Subscription is ${tenant.subscriptionStatus}`
    };
  }

  return { isValid: true };
}

/**
 * Validate tenant against middleware config
 */
export function validateTenantConfig(
  tenant: TenantInfo | null,
  config: TenantMiddlewareConfig
): {
  isValid: boolean;
  error?: string;
} {
  // Check if tenant is required
  if (config.requireTenant && !tenant) {
    return {
      isValid: false,
      error: 'Tenant is required for this route'
    };
  }

  if (!tenant) {
    return { isValid: true }; // No tenant required
  }

  // Validate basic tenant status
  const basicValidation = validateTenantSync(tenant);
  if (!basicValidation.isValid) {
    return basicValidation;
  }

  // Check allowed plans
  if (config.allowedPlans && config.allowedPlans.length > 0) {
    if (!tenant.plan || !config.allowedPlans.includes(tenant.plan)) {
      return {
        isValid: false,
        error: `Plan ${tenant.plan} is not allowed for this route`
      };
    }
  }

  // Check required features
  if (config.requiredFeatures && config.requiredFeatures.length > 0) {
    if (!hasAllFeatures(tenant, config.requiredFeatures)) {
      const missingFeatures = config.requiredFeatures.filter(
        feature => !tenant.features.includes(feature)
      );
      return {
        isValid: false,
        error: `Missing required features: ${missingFeatures.join(', ')}`
      };
    }
  }

  return { isValid: true };
}

/**
 * Get user's role and permissions in the organization
 */
export async function getUserTenantPermissions(
  userId: string,
  organizationId: string
): Promise<{
  role: 'owner' | 'admin' | 'manager' | 'staff' | null;
  permissions: string[];
}> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          }
        }
      }
    );

    const { data: user, error } = await supabase
      .from('users')
      .select('role, permissions')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (error || !user) {
      return { role: null, permissions: [] };
    }

    return {
      role: user.role,
      permissions: Array.isArray(user.permissions) ? user.permissions : []
    };
  } catch (error) {
    console.error('Error fetching user tenant permissions:', error);
    return { role: null, permissions: [] };
  }
}

/**
 * Check if user has permission in the organization
 */
export function hasPermission(permissions: string[], permission: string): boolean {
  return permissions.includes(permission) || permissions.includes('*');
}

/**
 * Check if user has role with sufficient privileges
 */
export function hasMinimumRole(
  userRole: string,
  requiredRole: 'owner' | 'admin' | 'manager' | 'staff'
): boolean {
  const roleHierarchy = {
    owner: 4,
    admin: 3,
    manager: 2,
    staff: 1
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole];

  return userLevel >= requiredLevel;
}

/**
 * Validate organization membership
 */
export async function validateOrganizationMembership(
  userId: string,
  organizationId: string
): Promise<{
  isMember: boolean;
  isActive: boolean;
  role?: string;
}> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          }
        }
      }
    );

    const { data: user, error } = await supabase
      .from('users')
      .select('role, is_active')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (error || !user) {
      return {
        isMember: false,
        isActive: false
      };
    }

    return {
      isMember: true,
      isActive: user.is_active,
      role: user.role
    };
  } catch (error) {
    console.error('Error validating organization membership:', error);
    return {
      isMember: false,
      isActive: false
    };
  }
} 