export interface Setting {
  id: string;
  organizationId: string;
  userId?: string;
  category: SettingCategory;
  key: string;
  value: any;
  isGlobal: boolean;
  createdAt: string;
  updatedAt: string;
}

export type SettingCategory = 
  | 'account'
  | 'notifications'
  | 'privacy'
  | 'appearance'
  | 'preferences'
  | 'integrations'
  | 'security'
  | 'organization';

export interface UserAccountSettings {
  profile: {
    displayName: string;
    email: string;
    phone?: string;
    timezone: string;
    language: string;
    avatarUrl?: string;
  };
  preferences: {
    dateFormat: string;
    timeFormat: string;
    currency: string;
    numberFormat: string;
  };
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
    types: {
      orderUpdates: boolean;
      systemAlerts: boolean;
      marketing: boolean;
      security: boolean;
      teamInvitations: boolean;
      roleChanges: boolean;
    };
  };
  push: {
    enabled: boolean;
    types: {
      orderUpdates: boolean;
      systemAlerts: boolean;
      teamNotifications: boolean;
    };
  };
  sms: {
    enabled: boolean;
    types: {
      criticalAlerts: boolean;
      orderUpdates: boolean;
    };
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'organization' | 'private';
  dataSharing: {
    analytics: boolean;
    marketing: boolean;
    thirdParty: boolean;
  };
  sessionSettings: {
    rememberMe: boolean;
    sessionTimeout: number; // minutes
    logoutOnClose: boolean;
  };
}

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  density: 'compact' | 'comfortable' | 'spacious';
  sidebar: {
    collapsed: boolean;
    position: 'left' | 'right';
    pinned: boolean;
  };
  animations: {
    enabled: boolean;
    reducedMotion: boolean;
  };
  customization: {
    accentColor: string;
    fontFamily: string;
    fontSize: number;
  };
}

export interface IntegrationSettings {
  stripe: {
    enabled: boolean;
    webhookUrl?: string;
    testMode: boolean;
  };
  email: {
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
    settings: Record<string, any>;
  };
  sms: {
    provider: 'twilio' | 'aws-sns';
    settings: Record<string, any>;
  };
  analytics: {
    googleAnalytics: {
      enabled: boolean;
      trackingId?: string;
    };
    customEvents: boolean;
  };
  storage: {
    provider: 'supabase' | 'aws-s3' | 'cloudinary';
    settings: Record<string, any>;
  };
}

export interface SecuritySettings {
  twoFactor: {
    enabled: boolean;
    method: 'app' | 'sms' | 'email';
    backupCodes: string[];
  };
  sessions: {
    maxActiveSessions: number;
    requireReauth: boolean;
    logSuspiciousActivity: boolean;
  };
  apiKeys: {
    enabled: boolean;
    keys: Array<{
      id: string;
      name: string;
      permissions: string[];
      lastUsed?: string;
      created: string;
    }>;
  };
  auditLog: {
    enabled: boolean;
    retentionDays: number;
    events: string[];
  };
}

export interface OrganizationSettings {
  branding: {
    logo?: string;
    favicon?: string;
    primaryColor: string;
    secondaryColor: string;
    customDomain?: string;
  };
  features: {
    multiTenant: boolean;
    ssoEnabled: boolean;
    apiAccess: boolean;
    webhooks: boolean;
    advancedReporting: boolean;
  };
  limits: {
    maxUsers: number;
    maxMenus: number;
    maxFileSize: number; // MB
    storageLimit: number; // GB
  };
  compliance: {
    gdprEnabled: boolean;
    ccpaEnabled: boolean;
    dataRetentionDays: number;
    anonymizeData: boolean;
  };
}

export interface SettingsFormData {
  account?: Partial<UserAccountSettings>;
  notifications?: Partial<NotificationSettings>;
  privacy?: Partial<PrivacySettings>;
  appearance?: Partial<AppearanceSettings>;
  integrations?: Partial<IntegrationSettings>;
  security?: Partial<SecuritySettings>;
  organization?: Partial<OrganizationSettings>;
}

export interface SettingsPageProps {
  organizationId: string;
  userId: string;
  userRole: string;
  initialSettings: Setting[];
}

export type SettingsUpdatePayload = {
  category: SettingCategory;
  key: string;
  value: any;
  isGlobal?: boolean;
}; 