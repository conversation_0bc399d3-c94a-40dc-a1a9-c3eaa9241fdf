# AI Coding Agent Rules - Restaurant SaaS Platform

## 🎯 Project Overview

This is a multi-tenant restaurant SaaS platform enabling restaurants to create subscription-based online menus with custom domains. All AI coding agents must follow these rules to ensure consistency, security, and best practices.

---

## 🛠️ Required MCP Servers & Usage

### **Core Development MCPs (MANDATORY)**

#### **1. Database Operations**

- **✅ ALWAYS USE**: `mcp_supabase-mcp` for all database operations
- **Purpose**: Database schema management, migrations, queries
- **Rules**:
  - Enable unsafe mode only for schema changes
  - Always use migrations for schema modifications
  - Test queries in safe mode first

#### **2. File Operations**

- **✅ ALWAYS USE**: Standard file tools (`read_file`, `edit_file`, `list_dir`)
- **Rules**:
  - Use parallel operations for multiple file reads
  - Always provide clear edit instructions
  - Follow project structure conventions

#### **3. Code Search & Navigation**

- **✅ ALWAYS USE**: `codebase_search` for semantic searches
- **✅ ALWAYS USE**: `grep_search` for exact pattern matching
- **Rules**:
  - Use semantic search for concepts/functionality
  - Use grep for exact function/variable names
  - Search before implementing to avoid duplication

### **Recommended MCP Servers (Install if Available)**

#### **Development Enhancement**

- **GitHub MCP**: For repository management, PR creation
- **Stripe MCP**: For payment integration testing
- **Docker MCP**: For containerization and deployment
- **Redis MCP**: For caching implementation

#### **Documentation & Testing**

- **Documentation MCP**: For API documentation generation
- **Testing MCP**: For automated test generation
- **Code Quality MCP**: For linting and formatting

---

## 🏗️ Technology Stack Requirements

### **Frontend (MANDATORY)**

```typescript
// Required versions and setup
- Next.js: "^14.2.0" (App Router ONLY)
- React: "^18.2.0"
- TypeScript: "^5.3.3"
- Tailwind CSS: "^3.4.1"
- Framer Motion: "^11.0.8"
```

### **Backend & Database (MANDATORY)**

```typescript
// Required services
- Supabase: Database, Auth, Storage
- Stripe: Payment processing
- Resend: Email delivery
- Cloudflare: DNS and domain management
```

### **Deployment (MANDATORY)**

```typescript
// Must use Netlify (not Vercel)
- Platform: Netlify
- CDN: Netlify Edge
- Functions: Netlify Functions
- Build: @netlify/plugin-nextjs
```

---

## 📁 Project Structure Rules

### **Directory Structure (MANDATORY)**

```
restaurant-saas-platform/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Auth pages group
│   ├── (dashboard)/       # Dashboard pages group
│   ├── restaurant/        # Restaurant public pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── restaurant/       # Restaurant-specific components
├── lib/                  # Utility libraries
│   ├── supabase/         # Supabase client & utilities
│   ├── stripe/           # Stripe integration
│   ├── validations/      # Zod schemas
│   └── utils.ts          # General utilities
├── types/                # TypeScript type definitions
├── hooks/                # Custom React hooks
├── docs/                 # Project documentation
└── netlify/              # Netlify configuration
    ├── functions/        # Serverless functions
    └── edge-functions/   # Edge functions
```

### **File Naming Conventions (MANDATORY)**

```typescript
// Components: PascalCase
RestaurantCard.tsx;
MenuItemForm.tsx;

// Pages: kebab-case
restaurant / [slug] / menu / page.tsx;
dashboard / settings / page.tsx;

// Utilities: camelCase
formatPrice.ts;
validateRestaurant.ts;

// Types: PascalCase with descriptive names
RestaurantWithMenus.ts;
SubscriptionStatus.ts;
```

---

## 🔐 Security Rules (CRITICAL)

### **Authentication & Authorization (MANDATORY)**

```typescript
// ALWAYS implement these patterns

// 1. Route Protection
export default async function ProtectedPage() {
  const supabase = createServerComponentClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    redirect('/login');
  }
  // Page content...
}

// 2. RLS Policies (Always enable)
-- ALWAYS create RLS policies for new tables
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their restaurants"
ON restaurants FOR ALL
USING (auth.uid() = user_id);

// 3. Input Validation (Always use Zod)
const restaurantSchema = z.object({
  name: z.string().min(1).max(100),
  slug: z.string().regex(/^[a-z0-9-]+$/),
  // Always validate all inputs
});
```

### **Data Protection (MANDATORY)**

- **NEVER** expose sensitive data in client components
- **ALWAYS** use environment variables for secrets
- **ALWAYS** validate inputs with Zod schemas
- **ALWAYS** sanitize user content before display

---

## 🎨 UI/UX Standards (MANDATORY)

### **Component Patterns**

```typescript
// ALWAYS use this component structure
interface ComponentProps {
  // Always define explicit props
  title: string;
  isLoading?: boolean;
  className?: string;
}

export function Component({
  title,
  isLoading = false,
  className,
}: ComponentProps) {
  return (
    <div className={cn("base-styles", className)}>
      {/* Always handle loading states */}
      {isLoading ? <Skeleton className="h-8 w-32" /> : <h1>{title}</h1>}
    </div>
  );
}
```

### **Styling Rules (MANDATORY)**

```css
/* ALWAYS use Tailwind utility classes */
/* NEVER write custom CSS unless absolutely necessary */

/* Good */
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">

/* Bad */
<div style={{display: 'flex', padding: '16px'}}>

/* ALWAYS use consistent spacing scale */
/* Use: p-1, p-2, p-4, p-6, p-8, p-12, p-16 */

/* ALWAYS use semantic color classes */
/* Primary: blue-600, Success: green-600, Error: red-600 */
```

### **Responsive Design (MANDATORY)**

```typescript
// ALWAYS implement mobile-first responsive design
<div
  className="
  grid 
  grid-cols-1 
  md:grid-cols-2 
  lg:grid-cols-3 
  gap-4 
  p-4
"
>
  {/* Always test on mobile, tablet, desktop */}
</div>
```

---

## 🗃️ Database Rules (CRITICAL)

### **Schema Design (MANDATORY)**

```sql
-- ALWAYS follow this table structure pattern
CREATE TABLE restaurants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ALWAYS add RLS policies
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;

-- ALWAYS add indexes for performance
CREATE INDEX idx_restaurants_user_id ON restaurants(user_id);
CREATE INDEX idx_restaurants_slug ON restaurants(slug);

-- ALWAYS add updated_at trigger
CREATE TRIGGER set_updated_at
  BEFORE UPDATE ON restaurants
  FOR EACH ROW
  EXECUTE FUNCTION set_updated_at();
```

### **Migration Rules (MANDATORY)**

- **ALWAYS** create migrations for schema changes
- **ALWAYS** use descriptive migration names
- **ALWAYS** test migrations on development first
- **NEVER** modify existing migrations

---

## 💳 Payment Integration Rules (CRITICAL)

### **Stripe Implementation (MANDATORY)**

```typescript
// ALWAYS use this Stripe pattern
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2023-10-16",
});

// ALWAYS handle webhooks securely
export async function POST(req: Request) {
  const sig = req.headers.get("stripe-signature");

  try {
    const event = stripe.webhooks.constructEvent(
      await req.text(),
      sig!,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    // ALWAYS handle all subscription events
    switch (event.type) {
      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted":
        // Handle subscription changes
        break;
    }

    return new Response("OK");
  } catch (error) {
    return new Response("Error", { status: 400 });
  }
}
```

### **Subscription Management (MANDATORY)**

- **ALWAYS** sync subscription status with database
- **ALWAYS** handle failed payments gracefully
- **ALWAYS** provide clear billing information to users
- **NEVER** expose Stripe secrets to client

---

## 🌐 Multi-tenancy Rules (CRITICAL)

### **Domain Routing (MANDATORY)**

```typescript
// ALWAYS implement this subdomain pattern
export async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const hostname = request.headers.get("host");

  // Check for restaurant subdomain
  if (hostname?.includes(".yourdomain.com")) {
    const subdomain = hostname.split(".")[0];

    // ALWAYS validate restaurant exists
    const restaurant = await getRestaurantBySlug(subdomain);
    if (!restaurant) {
      return NextResponse.redirect(new URL("/404", request.url));
    }

    // Rewrite to restaurant page
    url.pathname = `/restaurant/${subdomain}${url.pathname}`;
    return NextResponse.rewrite(url);
  }
}
```

### **Data Isolation (MANDATORY)**

```typescript
// ALWAYS filter by restaurant ownership
export async function getMenuItems(restaurantId: string, userId: string) {
  const { data, error } = await supabase
    .from("menu_items")
    .select("*")
    .eq("restaurant_id", restaurantId)
    .eq("user_id", userId); // ALWAYS verify ownership

  if (error) throw error;
  return data;
}
```

---

## 🧪 Testing Rules (MANDATORY)

### **Test Structure (MANDATORY)**

```typescript
// ALWAYS write tests for critical functionality
// File: __tests__/components/RestaurantCard.test.tsx

import { render, screen } from "@testing-library/react";
import { RestaurantCard } from "@/components/RestaurantCard";

describe("RestaurantCard", () => {
  it("displays restaurant name and status", () => {
    const restaurant = {
      id: "1",
      name: "Test Restaurant",
      status: "active",
    };

    render(<RestaurantCard restaurant={restaurant} />);

    expect(screen.getByText("Test Restaurant")).toBeInTheDocument();
    expect(screen.getByText("active")).toBeInTheDocument();
  });
});
```

### **Testing Requirements (MANDATORY)**

- **ALWAYS** test critical user flows
- **ALWAYS** test payment integration
- **ALWAYS** test authentication
- **ALWAYS** test database operations
- **NEVER** commit code without tests for new features

---

## 📊 Performance Rules (MANDATORY)

### **Optimization Patterns (MANDATORY)**

```typescript
// ALWAYS implement loading states
function RestaurantList() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadRestaurants() {
      setIsLoading(true);
      try {
        const data = await fetchRestaurants();
        setRestaurants(data);
      } finally {
        setIsLoading(false);
      }
    }
    loadRestaurants();
  }, []);

  if (isLoading) {
    return <RestaurantListSkeleton />;
  }

  return <RestaurantGrid restaurants={restaurants} />;
}

// ALWAYS use React.memo for expensive components
export const RestaurantCard = React.memo(function RestaurantCard({
  restaurant,
}: RestaurantCardProps) {
  return <div className="restaurant-card">{/* Component content */}</div>;
});
```

### **Image Optimization (MANDATORY)**

```typescript
// ALWAYS use Next.js Image component
import Image from "next/image";

<Image
  src={restaurant.image}
  alt={restaurant.name}
  width={400}
  height={300}
  className="rounded-lg"
  priority={isFoldImage} // ALWAYS set priority for above-fold images
/>;
```

---

## 📝 Documentation Rules (MANDATORY)

### **Code Documentation (MANDATORY)**

```typescript
/**
 * Creates a new restaurant with menu items
 * @param restaurantData - Restaurant information
 * @param userId - Owner's user ID
 * @returns Created restaurant with generated slug
 * @throws {Error} When restaurant name is already taken
 */
export async function createRestaurant(
  restaurantData: CreateRestaurantInput,
  userId: string
): Promise<Restaurant> {
  // ALWAYS document complex functions
}
```

### **API Documentation (MANDATORY)**

```typescript
// ALWAYS document API routes
/**
 * POST /api/restaurants
 * Creates a new restaurant
 *
 * @body {
 *   name: string,
 *   description?: string,
 *   cuisine_type: string
 * }
 *
 * @returns {
 *   success: boolean,
 *   restaurant: Restaurant
 * }
 */
export async function POST(request: Request) {
  // Implementation...
}
```

---

## 🚀 Deployment Rules (MANDATORY)

### **Netlify Configuration (MANDATORY)**

```toml
# ALWAYS use this netlify.toml configuration
[build]
  command = "npm run build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"

# ALWAYS configure redirects for subdomains
[[redirects]]
  from = "https://:restaurant.yourdomain.com/*"
  to = "/restaurant/:restaurant/:splat"
  status = 200
  force = true
```

### **Environment Variables (MANDATORY)**

```bash
# ALWAYS set these environment variables
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
NEXT_PUBLIC_APP_URL=
```

---

## ❌ Forbidden Practices (NEVER DO)

### **Code Anti-patterns**

- **NEVER** use `any` type in TypeScript
- **NEVER** disable ESLint rules without justification
- **NEVER** commit secrets or API keys
- **NEVER** use `dangerouslySetInnerHTML` without sanitization
- **NEVER** skip input validation
- **NEVER** hardcode URLs or configuration

### **Database Anti-patterns**

- **NEVER** disable RLS without security review
- **NEVER** use raw SQL without parameterization
- **NEVER** store sensitive data without encryption
- **NEVER** create tables without proper indexing

### **Security Anti-patterns**

- **NEVER** trust client-side data
- **NEVER** expose admin functions to regular users
- **NEVER** store passwords in plain text
- **NEVER** skip CORS configuration

---

## 🔍 Code Review Checklist (MANDATORY)

Before committing code, ALWAYS verify:

### **✅ Security Checklist**

- [ ] All inputs are validated with Zod schemas
- [ ] Authentication is properly implemented
- [ ] RLS policies are enabled and tested
- [ ] No secrets are exposed to client
- [ ] CORS is properly configured

### **✅ Performance Checklist**

- [ ] Images are optimized with Next.js Image
- [ ] Loading states are implemented
- [ ] Database queries are optimized
- [ ] Proper caching is implemented
- [ ] Bundle size impact is minimal

### **✅ Code Quality Checklist**

- [ ] TypeScript types are properly defined
- [ ] Components follow naming conventions
- [ ] Functions are properly documented
- [ ] Tests are written for new features
- [ ] Code is properly formatted

### **✅ User Experience Checklist**

- [ ] Responsive design is implemented
- [ ] Error states are handled gracefully
- [ ] Loading states provide feedback
- [ ] Accessibility standards are met
- [ ] Forms have proper validation

---

## 🔄 Development Workflow (MANDATORY)

### **Git Workflow**

```bash
# ALWAYS follow this branch naming
feature/restaurant-menu-builder
bugfix/payment-webhook-timeout
hotfix/security-vulnerability

# ALWAYS write descriptive commit messages
git commit -m "feat(restaurant): add menu item reordering functionality

- Implement drag-and-drop for menu items
- Add position field to menu_items table
- Update API to handle position updates
- Add tests for reordering functionality"
```

### **Testing Workflow**

```bash
# ALWAYS run these before committing
npm run type-check    # TypeScript compilation
npm run lint         # ESLint check
npm run test         # Unit tests
npm run test:e2e     # End-to-end tests
npm run build        # Production build
```

---

## 📚 Learning Resources (REFERENCE)

### **Official Documentation**

- [Next.js App Router](https://nextjs.org/docs/app)
- [Supabase Documentation](https://supabase.com/docs)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Netlify Documentation](https://docs.netlify.com/)

### **Project-Specific Guides**

- `docs/USER_STORIES.md` - Feature requirements
- `docs/TICKETS.md` - Development tasks
- `docs/NETLIFY_DEPLOYMENT.md` - Deployment guide
- `TIMELINE.md` - Project timeline

---

## 🎯 Success Metrics (TRACK ALWAYS)

### **Code Quality Metrics**

- TypeScript strict mode: 100% compliance
- Test coverage: >80% for critical paths
- ESLint errors: 0 warnings/errors
- Build time: <3 minutes
- Bundle size: <500KB initial load

### **Performance Metrics**

- Core Web Vitals: All green
- Time to Interactive: <3 seconds
- Database query time: <100ms average
- API response time: <200ms average

### **Security Metrics**

- 0 known vulnerabilities
- 100% RLS policy coverage
- All inputs validated
- No exposed secrets

---

**Remember**: These rules ensure we build a secure, performant, and maintainable restaurant SaaS platform. Always prioritize user security and experience in every decision.
