import { NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's organization ID from database
    const supabase = createClient()
    const { data: userProfile, error } = await supabase
      .from('users')
      .select('organization_id')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Error fetching user organization:', error)
      return NextResponse.json({ error: 'Failed to fetch user data' }, { status: 500 })
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        avatar_url: user.avatar_url,
        organization_id: userProfile?.organization_id || null,
        created_at: user.created_at,
        updated_at: user.updated_at,
      }
    })
  } catch (error) {
    console.error('Error in /api/auth/user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 