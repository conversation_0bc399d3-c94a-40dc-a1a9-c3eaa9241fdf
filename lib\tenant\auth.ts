import { createServerClient } from '@supabase/ssr';
import { cookies, headers } from 'next/headers';
import { TenantInfo, TenantRequestContext } from '@/types/tenant';
import { detectTenant } from './detection';
import { validateTenant, getUserTenantPermissions } from './validation';

/**
 * Get current tenant and user context for server components
 */
export async function getTenantContext(): Promise<TenantRequestContext | null> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          }
        }
      }
    );

    // Get current user (secure authentication)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return null;
    }

    // Detect tenant
    const headersList = headers();
    const host = headersList.get('host');
    const tenantDetection = await detectTenant(host);

    if (!tenantDetection.organization) {
      return null;
    }

    // Validate tenant
    const tenantValidation = await validateTenant(tenantDetection.organization);
    if (!tenantValidation.isValid) {
      return null;
    }

    // Get user permissions in this organization
    const permissions = await getUserTenantPermissions(
      user.id,
      tenantDetection.organization.organizationId
    );

    if (!permissions.role) {
      return null;
    }

    return {
      tenant: tenantDetection.organization,
      user: {
        id: user.id,
        role: permissions.role,
        permissions: permissions.permissions
      }
    };
  } catch (error) {
    console.error('Error getting tenant context:', error);
    return null;
  }
}

/**
 * Require tenant context (throws error if not available)
 */
export async function requireTenantContext(): Promise<TenantRequestContext> {
  const context = await getTenantContext();
  if (!context) {
    throw new Error('Tenant context required');
  }
  return context;
}

/**
 * Get tenant from headers (for middleware use)
 */
export function getTenantFromHeaders(headersList: Headers): {
  tenantId?: string;
  tenantSlug?: string;
  tenantType?: string;
  organizationId?: string;
  userRole?: string;
} {
  return {
    tenantId: headersList.get('x-tenant-id') || undefined,
    tenantSlug: headersList.get('x-tenant-slug') || undefined,
    tenantType: headersList.get('x-tenant-type') || undefined,
    organizationId: headersList.get('x-organization-id') || undefined,
    userRole: headersList.get('x-user-role') || undefined,
  };
}

/**
 * Check if user has specific permission in current tenant
 */
export async function hasPermissionInTenant(permission: string): Promise<boolean> {
  const context = await getTenantContext();
  if (!context) {
    return false;
  }

  // Owners have all permissions
  if (context.user.role === 'owner') {
    return true;
  }

  // Check explicit permissions
  return context.user.permissions.includes(permission) || 
         context.user.permissions.includes('*');
}

/**
 * Check if user has minimum role in current tenant
 */
export async function hasMinimumRoleInTenant(
  requiredRole: 'owner' | 'admin' | 'manager' | 'staff'
): Promise<boolean> {
  const context = await getTenantContext();
  if (!context) {
    return false;
  }

  const roleHierarchy = {
    owner: 4,
    admin: 3,
    manager: 2,
    staff: 1
  };

  const userLevel = roleHierarchy[context.user.role];
  const requiredLevel = roleHierarchy[requiredRole];

  return userLevel >= requiredLevel;
}

/**
 * Get current tenant info only
 */
export async function getCurrentTenant(): Promise<TenantInfo | null> {
  const context = await getTenantContext();
  return context?.tenant || null;
}

/**
 * Check if feature is available in current tenant
 */
export async function hasFeatureInTenant(feature: string): Promise<boolean> {
  const tenant = await getCurrentTenant();
  if (!tenant) {
    return false;
  }
  return tenant.features.includes(feature);
}

/**
 * Require specific permission in tenant
 */
export async function requirePermissionInTenant(permission: string): Promise<void> {
  const hasPermission = await hasPermissionInTenant(permission);
  if (!hasPermission) {
    throw new Error(`Permission '${permission}' required`);
  }
}

/**
 * Require minimum role in tenant
 */
export async function requireRoleInTenant(
  role: 'owner' | 'admin' | 'manager' | 'staff'
): Promise<void> {
  const hasRole = await hasMinimumRoleInTenant(role);
  if (!hasRole) {
    throw new Error(`Role '${role}' or higher required`);
  }
}

/**
 * Require specific feature in tenant
 */
export async function requireFeatureInTenant(feature: string): Promise<void> {
  const hasFeature = await hasFeatureInTenant(feature);
  if (!hasFeature) {
    throw new Error(`Feature '${feature}' not available in current plan`);
  }
} 