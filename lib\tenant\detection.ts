import { TenantDetectionResult, TenantInfo } from '@/types/tenant';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * Extract subdomain from host header
 */
export function extractSubdomain(host: string | null): string | null {
  if (!host) return null;
  
  // Remove port if present
  const hostname = host.split(':')[0];
  
  // Split by dots and check if we have a subdomain
  const parts = hostname.split('.');
  
  // For development (localhost)
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    if (parts.length >= 2) {
      const subdomain = parts[0];
      if (!['www', 'api', 'admin'].includes(subdomain)) {
        return subdomain;
      }
    }
    return null;
  }
  
  // For Netlify domains (site-name.netlify.app)
  if (hostname.endsWith('netlify.app')) {
    const siteName = hostname.replace('.netlify.app', '');
    if (siteName.includes('-')) {
      const [org] = siteName.split('-');
      if (!['www', 'api', 'admin'].includes(org)) {
        return org;
      }
    }
    return null;
  }
  
  // For production domains (subdomain.domain.com)
  if (parts.length >= 3) {
    const subdomain = parts[0];
    if (!['www', 'api', 'admin'].includes(subdomain)) {
      return subdomain;
    }
  }
  
  return null;
}

/**
 * Detect tenant from request host
 */
export async function detectTenant(host: string | null): Promise<TenantDetectionResult> {
  const subdomain = extractSubdomain(host);
  
  // Try subdomain detection first
  if (subdomain) {
    const organization = await getOrganizationBySlug(subdomain);
    if (organization) {
      return {
        type: 'subdomain',
        value: subdomain,
        organization
      };
    }
  }
  
  // Try custom domain detection
  if (host) {
    const hostname = host.split(':')[0];
    const organization = await getOrganizationByCustomDomain(hostname);
    if (organization) {
      return {
        type: 'custom_domain',
        value: hostname,
        organization
      };
    }
  }
  
  return { type: 'none' };
}

/**
 * Get organization by slug
 */
async function getOrganizationBySlug(slug: string): Promise<TenantInfo | null> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          }
        }
      }
    );

    const { data: organization, error } = await supabase
      .from('organizations')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error || !organization) {
      return null;
    }

    return transformOrganizationToTenant(organization);
  } catch (error) {
    console.error('Error fetching organization by slug:', error);
    return null;
  }
}

/**
 * Get organization by custom domain
 */
async function getOrganizationByCustomDomain(domain: string): Promise<TenantInfo | null> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          }
        }
      }
    );

    const { data: organization, error } = await supabase
      .from('organizations')
      .select('*')
      .eq('custom_domain', domain)
      .single();

    if (error || !organization) {
      return null;
    }

    return transformOrganizationToTenant(organization);
  } catch (error) {
    console.error('Error fetching organization by custom domain:', error);
    return null;
  }
}

/**
 * Transform database organization to TenantInfo
 */
function transformOrganizationToTenant(organization: any): TenantInfo {
  const features = getPlanFeatures(organization.plan_id || 'trial');
  
  return {
    organizationId: organization.id,
    slug: organization.slug,
    customDomain: organization.custom_domain,
    subscriptionStatus: organization.subscription_status,
    plan: organization.plan_id,
    features,
    trialEndsAt: organization.trial_ends_at ? new Date(organization.trial_ends_at) : undefined,
    isActive: organization.subscription_status === 'active' || 
             (organization.subscription_status === 'trial' && 
              (!organization.trial_ends_at || new Date(organization.trial_ends_at) > new Date()))
  };
}

/**
 * Get features for a given plan
 */
function getPlanFeatures(planId: string): string[] {
  const planFeatureMap: Record<string, string[]> = {
    'trial': ['basic_menu', 'up_to_5_items'],
    'starter': ['basic_menu', 'up_to_50_items', 'custom_domain'],
    'professional': ['advanced_menu', 'unlimited_items', 'custom_domain', 'analytics', 'team_members'],
    'enterprise': ['advanced_menu', 'unlimited_items', 'custom_domain', 'analytics', 'team_members', 'api_access', 'white_label']
  };

  return planFeatureMap[planId] || planFeatureMap['trial'];
} 