import { TenantInfo } from '@/types/tenant';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * Create tenant-scoped database client
 * Note: Tenant isolation is handled by RLS policies at the database level
 */
export function createTenantClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value;
        }
      }
    }
  );
}

/**
 * Generate tenant-specific cache key
 */
export function getTenantCacheKey(organizationId: string, key: string): string {
  return `tenant:${organizationId}:${key}`;
}

/**
 * Generate tenant-specific session key
 */
export function getTenantSessionKey(organizationId: string, userId: string): string {
  return `session:${organizationId}:${userId}`;
}

/**
 * Generate tenant-aware URL
 */
export function getTenantUrl(tenant: TenantInfo, path: string = '/'): string {
  // Use custom domain if available
  if (tenant.customDomain) {
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    return `${protocol}://${tenant.customDomain}${path}`;
  }

  // Use subdomain
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const url = new URL(baseUrl);
  url.hostname = `${tenant.slug}.${url.hostname}`;
  url.pathname = path;

  return url.toString();
}

/**
 * Generate tenant-aware asset path
 */
export function getTenantAssetPath(tenant: TenantInfo, assetPath: string): string {
  const basePath = process.env.NEXT_PUBLIC_CDN_URL || '';
  return `${basePath}/organizations/${tenant.organizationId}/${assetPath}`;
}

/**
 * Get tenant-specific upload path
 */
export function getTenantUploadPath(organizationId: string, type: 'logos' | 'menus' | 'items'): string {
  return `organizations/${organizationId}/${type}`;
}

/**
 * Tenant-aware query builder for Supabase
 */
export class TenantQueryBuilder {
  private organizationId: string;
  private supabase: any;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.supabase = createTenantClient();
  }

  /**
   * Get menus for current tenant
   */
  getMenus() {
    return this.supabase
      .from('menus')
      .select('*')
      .eq('organization_id', this.organizationId)
      .order('display_order', { ascending: true });
  }

  /**
   * Get menu categories for current tenant
   */
  getMenuCategories(menuId?: string) {
    let query = this.supabase
      .from('menu_categories')
      .select(`
        *,
        menus!inner(organization_id)
      `)
      .eq('menus.organization_id', this.organizationId);

    if (menuId) {
      query = query.eq('menu_id', menuId);
    }

    return query.order('display_order', { ascending: true });
  }

  /**
   * Get menu items for current tenant
   */
  getMenuItems(categoryId?: string) {
    let query = this.supabase
      .from('menu_items')
      .select(`
        *,
        menu_categories!inner(
          menu_id,
          menus!inner(organization_id)
        )
      `)
      .eq('menu_categories.menus.organization_id', this.organizationId);

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    return query.order('display_order', { ascending: true });
  }

  /**
   * Get organization users
   */
  getUsers() {
    return this.supabase
      .from('users')
      .select('*')
      .eq('organization_id', this.organizationId)
      .eq('is_active', true)
      .order('created_at', { ascending: true });
  }

  /**
   * Get subscription info
   */
  getSubscription() {
    return this.supabase
      .from('subscriptions')
      .select('*')
      .eq('organization_id', this.organizationId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
  }
}

/**
 * Validate tenant access to resource
 */
export async function validateTenantAccess(
  organizationId: string,
  resourceTable: string,
  resourceId: string
): Promise<boolean> {
  try {
    const supabase = createTenantClient();
    
    // For resources that belong directly to organization
    const directOrgTables = ['menus', 'subscriptions', 'invoices'];
    
    if (directOrgTables.includes(resourceTable)) {
      const { data, error } = await supabase
        .from(resourceTable as any)
        .select('id')
        .eq('id', resourceId)
        .eq('organization_id', organizationId)
        .single();

      return !error && !!data;
    }

    // For nested resources (menu_categories, menu_items)
    if (resourceTable === 'menu_categories') {
      const { data, error } = await supabase
        .from('menu_categories')
        .select(`
          id,
          menus!inner(organization_id)
        `)
        .eq('id', resourceId)
        .eq('menus.organization_id', organizationId)
        .single();

      return !error && !!data;
    }

    if (resourceTable === 'menu_items') {
      const { data, error } = await supabase
        .from('menu_items')
        .select(`
          id,
          menu_categories!inner(
            menus!inner(organization_id)
          )
        `)
        .eq('id', resourceId)
        .eq('menu_categories.menus.organization_id', organizationId)
        .single();

      return !error && !!data;
    }

    return false;
  } catch (error) {
    console.error('Error validating tenant access:', error);
    return false;
  }
}

/**
 * Get tenant-safe redirect URL
 */
export function getTenantRedirectUrl(
  tenant: TenantInfo | null,
  path: string,
  fallbackUrl: string = '/'
): string {
  if (!tenant) {
    return fallbackUrl;
  }

  try {
    return getTenantUrl(tenant, path);
  } catch (error) {
    console.error('Error generating tenant URL:', error);
    return fallbackUrl;
  }
}

/**
 * Extract organization ID from various sources
 */
export function extractOrganizationId(
  tenant?: TenantInfo | null,
  params?: { organizationId?: string },
  headers?: Headers
): string | null {
  // Try tenant info first
  if (tenant?.organizationId) {
    return tenant.organizationId;
  }

  // Try URL params
  if (params?.organizationId) {
    return params.organizationId;
  }

  // Try headers (for API routes)
  if (headers?.get('x-organization-id')) {
    return headers.get('x-organization-id');
  }

  return null;
} 