import { useState, useEffect, useCallback } from 'react';
import { Setting, SettingsUpdatePayload, SettingCategory } from '@/types/settings';
import { toast } from '@/components/ui/use-toast';

interface UseSettingsOptions {
  organizationId: string;
  autoSave?: boolean;
  debounceMs?: number;
}

interface UseSettingsReturn {
  settings: Setting[];
  loading: boolean;
  error: string | null;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  updateMultipleSettings: (updates: SettingsUpdatePayload[]) => Promise<void>;
  getSetting: (category: SettingCategory, key: string) => any;
  resetSettings: (category?: SettingCategory) => Promise<void>;
  refreshSettings: () => Promise<void>;
  hasUnsavedChanges: boolean;
  saveChanges: () => Promise<void>;
}

export function useSettings({ 
  organizationId, 
  autoSave = false, 
  debounceMs = 500 
}: UseSettingsOptions): UseSettingsReturn {
  const [settings, setSettings] = useState<Setting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pendingUpdates, setPendingUpdates] = useState<SettingsUpdatePayload[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Fetch settings from API
  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/settings?organizationId=${organizationId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch settings: ${response.statusText}`);
      }

      const data = await response.json();
      setSettings(data.settings || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch settings';
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  // Get a specific setting value
  const getSetting = useCallback((category: SettingCategory, key: string): any => {
    // First check pending updates
    const pendingUpdate = pendingUpdates.find(
      update => update.category === category && update.key === key
    );
    
    if (pendingUpdate) {
      return pendingUpdate.value;
    }

    // Then check current settings
    const setting = settings.find(
      s => s.category === category && s.key === key
    );

    return setting?.value;
  }, [settings, pendingUpdates]);

  // Update a single setting
  const updateSetting = useCallback(async (
    category: SettingCategory, 
    key: string, 
    value: any
  ) => {
    const update: SettingsUpdatePayload = { category, key, value };
    
    // Add to pending updates for optimistic UI
    setPendingUpdates(prev => {
      const filtered = prev.filter(u => !(u.category === category && u.key === key));
      return [...filtered, update];
    });
    
    setHasUnsavedChanges(true);

    if (autoSave) {
      await saveChanges();
    }
  }, [autoSave]);

  // Update multiple settings
  const updateMultipleSettings = useCallback(async (updates: SettingsUpdatePayload[]) => {
    // Add to pending updates for optimistic UI
    setPendingUpdates(prev => {
      const filtered = prev.filter(prevUpdate => 
        !updates.some(newUpdate => 
          newUpdate.category === prevUpdate.category && newUpdate.key === prevUpdate.key
        )
      );
      return [...filtered, ...updates];
    });
    
    setHasUnsavedChanges(true);

    if (autoSave) {
      await saveChanges();
    }
  }, [autoSave]);

  // Save pending changes to the server
  const saveChanges = useCallback(async () => {
    if (pendingUpdates.length === 0) {
      return;
    }

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
          updates: pendingUpdates,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save settings: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Update settings with the response
      setSettings(prev => {
        const newSettings = [...prev];
        
        data.settings.forEach((updatedSetting: Setting) => {
          const index = newSettings.findIndex(s => 
            s.category === updatedSetting.category && 
            s.key === updatedSetting.key &&
            s.userId === updatedSetting.userId
          );
          
          if (index >= 0) {
            newSettings[index] = updatedSetting;
          } else {
            newSettings.push(updatedSetting);
          }
        });
        
        return newSettings;
      });

      // Clear pending updates
      setPendingUpdates([]);
      setHasUnsavedChanges(false);
      
      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully.",
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save settings';
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    }
  }, [organizationId, pendingUpdates]);

  // Reset settings (remove user overrides)
  const resetSettings = useCallback(async (category?: SettingCategory) => {
    try {
      const response = await fetch('/api/settings/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
          category,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to reset settings: ${response.statusText}`);
      }

      // Refresh settings after reset
      await fetchSettings();
      
      toast({
        title: "Settings reset",
        description: category 
          ? `${category} settings have been reset to defaults.`
          : "All settings have been reset to defaults.",
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset settings';
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    }
  }, [organizationId, fetchSettings]);

  // Refresh settings from server
  const refreshSettings = useCallback(async () => {
    await fetchSettings();
  }, [fetchSettings]);

  // Auto-save with debouncing
  useEffect(() => {
    if (!autoSave || pendingUpdates.length === 0) {
      return;
    }

    const timeoutId = setTimeout(() => {
      saveChanges();
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [pendingUpdates, autoSave, debounceMs, saveChanges]);

  // Initial fetch
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    loading,
    error,
    updateSetting,
    updateMultipleSettings,
    getSetting,
    resetSettings,
    refreshSettings,
    hasUnsavedChanges,
    saveChanges,
  };
} 