'use client';
/* eslint-disable react/no-unescaped-entities */

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { SettingCategory } from '@/types/settings';
import { 
  Building, 
  Palette, 
  Users, 
  Shield, 
  Database, 
  RotateCcw,
  Upload,

  CheckCircle,
  Crown
} from 'lucide-react';

interface OrganizationSettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  organizationName: string;
  onReset: () => void;
}

export function OrganizationSettings({
  getSetting,
  updateSetting,
  organizationName,
  onReset,
}: OrganizationSettingsProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Get current settings with defaults
  const organization = getSetting('organization', 'settings') || {
    branding: {
      logo: '',
      favicon: '',
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      customDomain: '',
    },
    features: {
      multiTenant: true,
      ssoEnabled: false,
      apiAccess: true,
      webhooks: true,
      advancedReporting: false,
    },
    limits: {
      maxUsers: 50,
      maxMenus: 10,
      maxFileSize: 10, // MB
      storageLimit: 5, // GB
    },
    compliance: {
      gdprEnabled: true,
      ccpaEnabled: false,
      dataRetentionDays: 365,
      anonymizeData: true,
    },
  };

  const updateBranding = async (field: string, value: any) => {
    await updateSetting('organization', 'settings', {
      ...organization,
      branding: {
        ...organization.branding,
        [field]: value,
      },
    });
  };

  const updateFeatures = async (field: string, value: any) => {
    await updateSetting('organization', 'settings', {
      ...organization,
      features: {
        ...organization.features,
        [field]: value,
      },
    });
  };

  const updateLimits = async (field: string, value: any) => {
    await updateSetting('organization', 'settings', {
      ...organization,
      limits: {
        ...organization.limits,
        [field]: value,
      },
    });
  };

  const updateCompliance = async (field: string, value: any) => {
    await updateSetting('organization', 'settings', {
      ...organization,
      compliance: {
        ...organization.compliance,
        [field]: value,
      },
    });
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadLogo = async () => {
    if (!logoFile) return;
    
    // TODO: Implement logo upload to storage
    await updateBranding('logo', logoPreview);
    setLogoFile(null);
    setLogoPreview(null);
  };

  return (
    <div className="space-y-6">
      {/* Organization Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Organization Information
              </CardTitle>
              <CardDescription>
                Basic information about {organizationName}.
              </CardDescription>
            </div>
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              <Crown className="h-3 w-3 mr-1" />
              Admin Only
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Organization Name</Label>
              <Input value={organizationName} disabled />
            </div>
            <div className="space-y-2">
              <Label htmlFor="customDomain">Custom Domain</Label>
              <Input
                id="customDomain"
                value={organization.branding.customDomain}
                onChange={(e) => updateBranding('customDomain', e.target.value)}
                placeholder="your-domain.com"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Branding */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Branding & Appearance
              </CardTitle>
              <CardDescription>
                Customize your organization&apos;s visual identity.
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Upload */}
          <div className="space-y-3">
            <Label>Organization Logo</Label>
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 border rounded-lg flex items-center justify-center bg-muted">
                {logoPreview || organization.branding.logo ? (
                  <>
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img 
                      src={logoPreview || organization.branding.logo} 
                      alt="Logo" 
                      className="w-full h-full object-contain rounded-lg"
                    />
                  </>
                ) : (
                  <Building className="h-8 w-8 text-muted-foreground" />
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="logo-upload" className="cursor-pointer">
                  <div className="flex items-center gap-2 px-4 py-2 border rounded-md hover:bg-accent">
                    <Upload className="h-4 w-4" />
                    Upload Logo
                  </div>
                  <input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoChange}
                  />
                </Label>
                {logoFile && (
                  <Button size="sm" onClick={uploadLogo}>
                    Save Logo
                  </Button>
                )}
                <p className="text-xs text-muted-foreground">
                  PNG or SVG recommended. Max size 2MB.
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Color Scheme */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">Primary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={organization.branding.primaryColor}
                  onChange={(e) => updateBranding('primaryColor', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={organization.branding.primaryColor}
                  onChange={(e) => updateBranding('primaryColor', e.target.value)}
                  placeholder="#3b82f6"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="secondaryColor">Secondary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="secondaryColor"
                  type="color"
                  value={organization.branding.secondaryColor}
                  onChange={(e) => updateBranding('secondaryColor', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={organization.branding.secondaryColor}
                  onChange={(e) => updateBranding('secondaryColor', e.target.value)}
                  placeholder="#64748b"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Features & Capabilities
          </CardTitle>
          <CardDescription>
            Enable or disable organization-wide features.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Multi-tenant Support</Label>
              <p className="text-sm text-muted-foreground">
                Allow multiple organizations on this instance
              </p>
            </div>
            <Switch
              checked={organization.features.multiTenant}
              onCheckedChange={(checked) => updateFeatures('multiTenant', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Single Sign-On (SSO)</Label>
              <p className="text-sm text-muted-foreground">
                Enable SAML/OAuth authentication
              </p>
            </div>
            <Switch
              checked={organization.features.ssoEnabled}
              onCheckedChange={(checked) => updateFeatures('ssoEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">API Access</Label>
              <p className="text-sm text-muted-foreground">
                Allow programmatic access via REST API
              </p>
            </div>
            <Switch
              checked={organization.features.apiAccess}
              onCheckedChange={(checked) => updateFeatures('apiAccess', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Webhooks</Label>
              <p className="text-sm text-muted-foreground">
                Send real-time notifications to external services
              </p>
            </div>
            <Switch
              checked={organization.features.webhooks}
              onCheckedChange={(checked) => updateFeatures('webhooks', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Advanced Reporting</Label>
              <p className="text-sm text-muted-foreground">
                Access detailed analytics and custom reports
              </p>
            </div>
            <Switch
              checked={organization.features.advancedReporting}
              onCheckedChange={(checked) => updateFeatures('advancedReporting', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Usage Limits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Usage Limits
          </CardTitle>
          <CardDescription>
            Set limits for organization resources and usage.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Maximum Users</Label>
              <span className="text-sm text-muted-foreground">
                {organization.limits.maxUsers} users
              </span>
            </div>
            <Slider
              value={[organization.limits.maxUsers]}
              onValueChange={(value) => updateLimits('maxUsers', value[0])}
              min={1}
              max={1000}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>1 user</span>
              <span>1000 users</span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Maximum Menus</Label>
              <span className="text-sm text-muted-foreground">
                {organization.limits.maxMenus} menus
              </span>
            </div>
            <Slider
              value={[organization.limits.maxMenus]}
              onValueChange={(value) => updateLimits('maxMenus', value[0])}
              min={1}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Max File Upload Size</Label>
              <span className="text-sm text-muted-foreground">
                {organization.limits.maxFileSize} MB
              </span>
            </div>
            <Slider
              value={[organization.limits.maxFileSize]}
              onValueChange={(value) => updateLimits('maxFileSize', value[0])}
              min={1}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Storage Limit</Label>
              <span className="text-sm text-muted-foreground">
                {organization.limits.storageLimit} GB
              </span>
            </div>
            <Slider
              value={[organization.limits.storageLimit]}
              onValueChange={(value) => updateLimits('storageLimit', value[0])}
              min={1}
              max={1000}
              step={1}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Compliance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data & Compliance
          </CardTitle>
          <CardDescription>
            Configure data retention and compliance settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">GDPR Compliance</Label>
              <p className="text-sm text-muted-foreground">
                Enable GDPR data protection features
              </p>
            </div>
            <Switch
              checked={organization.compliance.gdprEnabled}
              onCheckedChange={(checked) => updateCompliance('gdprEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">CCPA Compliance</Label>
              <p className="text-sm text-muted-foreground">
                Enable California Consumer Privacy Act features
              </p>
            </div>
            <Switch
              checked={organization.compliance.ccpaEnabled}
              onCheckedChange={(checked) => updateCompliance('ccpaEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Anonymize Data</Label>
              <p className="text-sm text-muted-foreground">
                Automatically anonymize personal data after retention period
              </p>
            </div>
            <Switch
              checked={organization.compliance.anonymizeData}
              onCheckedChange={(checked) => updateCompliance('anonymizeData', checked)}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Data Retention Period</Label>
              <span className="text-sm text-muted-foreground">
                {organization.compliance.dataRetentionDays} days
              </span>
            </div>
            <Slider
              value={[organization.compliance.dataRetentionDays]}
              onValueChange={(value) => updateCompliance('dataRetentionDays', value[0])}
              min={30}
              max={2555} // 7 years
              step={30}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>30 days</span>
              <span>7 years</span>
            </div>
          </div>

          <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Compliance Status:</strong> Your organization meets current data protection requirements. 
                Regular audits are recommended to maintain compliance.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 