"use client"

import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { CheckCircle, ArrowRight, Building } from "lucide-react"

export default function OnboardingCompletePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const success = searchParams.get("success")

  useEffect(() => {
    // If not coming from successful organization creation, redirect to organization setup
    if (!success) {
      router.push("/onboarding/organization")
    }
  }, [success, router])

  if (!success) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Success Icon */}
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Welcome to your restaurant!
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Your restaurant has been successfully created and you&apos;re ready to start building your menu.
          </p>
        </div>

        {/* Progress indicator */}
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
            <span className="ml-2 text-sm text-gray-600">Account created</span>
          </div>
          <div className="w-8 h-px bg-green-500"></div>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
            <span className="ml-2 text-sm text-gray-600">Restaurant setup</span>
          </div>
          <div className="w-8 h-px bg-green-500"></div>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
            <span className="ml-2 text-sm font-medium text-gray-900">Complete</span>
          </div>
        </div>

        {/* Success Card */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Building className="h-6 w-6 text-orange-600" />
              <h3 className="text-lg font-medium text-gray-900">What&apos;s next?</h3>
            </div>
            
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0"></div>
                <p>Your 14-day free trial has started</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0"></div>
                <p>A default menu structure has been created for you</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0"></div>
                <p>You can start adding menu items and customizing your restaurant</p>
              </div>
            </div>

            <div className="pt-4">
              <Button 
                asChild 
                className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
              >
                <Link href="/dashboard">
                  Go to Dashboard
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </Card>

        {/* Additional Actions */}
        <div className="text-center space-y-2">
          <p className="text-sm text-gray-500">
            Need help getting started?
          </p>
          <div className="space-x-4">
            <Link 
              href="/help" 
              className="text-sm text-orange-600 hover:text-orange-500"
            >
              View Documentation
            </Link>
            <span className="text-gray-300">•</span>
            <Link 
              href="/support" 
              className="text-sm text-orange-600 hover:text-orange-500"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 