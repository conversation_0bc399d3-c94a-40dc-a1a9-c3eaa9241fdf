'use client'

import { AuthProvider } from '@/lib/contexts/auth-context'
import { SidebarProvider, Sidebar, MobileSidebarTrigger } from '@/components/layout/sidebar-navigation'
import { AdvancedSidebar } from '@/components/layout/sidebar-variants'
import { useSettings } from '@/hooks/use-settings'
import { Suspense } from 'react'

function DashboardLayoutContent({ children }: { children: React.ReactNode }) {
  // Get navigation preferences from localStorage with premium defaults
  const getNavigationSettings = () => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('navigation-settings')
      if (saved) {
        try {
          return JSON.parse(saved)
        } catch (e) {
          console.error('Error parsing navigation settings:', e)
        }
      }
    }
    return {
      variant: 'premium',
      style: 'premium',
      collapsed: false,
      position: 'left',
      showLabels: true,
      showBadges: true,
      compactMode: false,
      animations: true,
      autoCollapse: false,
      mobileOverlay: true,
    }
  }

  const navigation = getNavigationSettings()

  const isPremiumStyle = navigation.style === 'premium'
  const sidebarVariant = navigation.variant || 'premium'

  return (
    <SidebarProvider defaultCollapsed={navigation.collapsed}>
      <div className="flex h-screen bg-gray-50">
        {isPremiumStyle ? (
          <AdvancedSidebar variant={sidebarVariant as any} />
        ) : (
          <Sidebar variant={sidebarVariant as any} />
        )}
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-white shadow-sm border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-xl font-semibold">Dashboard</h1>
              <MobileSidebarTrigger />
            </div>
          </header>
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      <Suspense fallback={
        <div className="flex h-screen bg-gray-50">
          <div className="w-64 bg-gradient-to-b from-slate-900 to-slate-800 border-r border-slate-700/50">
            <div className="flex h-16 items-center justify-center border-b border-slate-700/50">
              <div className="animate-pulse bg-slate-600 h-8 w-32 rounded"></div>
            </div>
            <div className="p-4 space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="animate-pulse bg-slate-600 h-10 rounded-lg"></div>
              ))}
            </div>
          </div>
          <div className="flex-1 flex flex-col">
            <div className="bg-white shadow-sm border-b px-6 py-4">
              <div className="animate-pulse bg-gray-200 h-6 w-24 rounded"></div>
            </div>
            <div className="flex-1 p-6">
              <div className="animate-pulse space-y-4">
                <div className="bg-gray-200 h-8 w-48 rounded"></div>
                <div className="bg-gray-200 h-4 w-full rounded"></div>
                <div className="bg-gray-200 h-4 w-3/4 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      }>
        <DashboardLayoutContent>
          {children}
        </DashboardLayoutContent>
      </Suspense>
    </AuthProvider>
  )
}