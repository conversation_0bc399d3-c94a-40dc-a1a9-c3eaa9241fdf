import { createClient } from '@/lib/supabase/server';
import { Role, UserRole, RoleAuditEntry, CreateRoleData, UpdateRoleData, Permission } from '@/types/roles';

export class RoleQueryError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RoleQueryError';
  }
}

// Helper function to convert null to undefined
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

// Helper function to parse permissions from database JSON
function parsePermissions(permissions: any): Permission[] {
  if (!permissions) return [];
  if (Array.isArray(permissions)) return permissions;
  if (typeof permissions === 'string') {
    try {
      return JSON.parse(permissions);
    } catch {
      return [];
    }
  }
  return [];
}

/**
 * Get all roles for an organization
 */
export async function getRoles(organizationId: string): Promise<Role[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('roles')
    .select('*')
    .eq('organization_id', organizationId)
    .order('name');

  if (error) {
    throw new RoleQueryError(`Failed to fetch roles: ${error.message}`, error.code);
  }

  return (data || []).map(role => ({
    id: role.id,
    organizationId: role.organization_id,
    name: role.name,
    description: nullToUndefined(role.description),
    permissions: parsePermissions(role.permissions),
    isSystemRole: role.is_system_role,
    createdBy: undefined, // created_by field not available in database schema
    createdAt: role.created_at,
    updatedAt: role.updated_at,
  }));
}

/**
 * Get a specific role by ID
 */
export async function getRole(roleId: string, organizationId: string): Promise<Role | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('roles')
    .select('*')
    .eq('id', roleId)
    .eq('organization_id', organizationId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') return null; // Not found
    throw new RoleQueryError(`Failed to fetch role: ${error.message}`, error.code);
  }

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined, // created_by field not available in database schema
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

/**
 * Create a new role
 */
export async function createRole(
  organizationId: string,
  roleData: CreateRoleData,
  createdBy: string
): Promise<Role> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('roles')
    .insert({
      organization_id: organizationId,
      name: roleData.name,
      description: roleData.description || null,
      permissions: roleData.permissions as any,
      is_system_role: false,
    })
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new RoleQueryError('A role with this name already exists in the organization', error.code);
    }
    throw new RoleQueryError(`Failed to create role: ${error.message}`, error.code);
  }

  // Log the role creation
  await logRoleAction(createdBy, undefined, data.id, organizationId, 'role_created', undefined, roleData.permissions);

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined, // created_by field not available in database schema
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

/**
 * Update a role
 */
export async function updateRole(
  roleId: string,
  organizationId: string,
  updates: UpdateRoleData,
  updatedBy: string
): Promise<Role> {
  const supabase = createClient();
  
  // Get current role for audit logging
  const currentRole = await getRole(roleId, organizationId);
  if (!currentRole) {
    throw new RoleQueryError('Role not found', 'NOT_FOUND');
  }

  if (currentRole.isSystemRole) {
    throw new RoleQueryError('System roles cannot be modified', 'SYSTEM_ROLE');
  }

  const updateData: any = {};
  if (updates.name !== undefined) updateData.name = updates.name;
  if (updates.description !== undefined) updateData.description = updates.description || null;
  if (updates.permissions !== undefined) updateData.permissions = updates.permissions as any;

  const { data, error } = await supabase
    .from('roles')
    .update(updateData)
    .eq('id', roleId)
    .eq('organization_id', organizationId)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new RoleQueryError('A role with this name already exists in the organization', error.code);
    }
    throw new RoleQueryError(`Failed to update role: ${error.message}`, error.code);
  }

  // Log the role update
  await logRoleAction(
    updatedBy,
    undefined,
    roleId,
    organizationId,
    'role_updated',
    currentRole.permissions,
    updates.permissions || currentRole.permissions
  );

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined, // created_by field not available in database schema
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

/**
 * Delete a role
 */
export async function deleteRole(roleId: string, organizationId: string, deletedBy: string): Promise<void> {
  const supabase = createClient();
  
  // Get current role for audit logging
  const currentRole = await getRole(roleId, organizationId);
  if (!currentRole) {
    throw new RoleQueryError('Role not found', 'NOT_FOUND');
  }

  if (currentRole.isSystemRole) {
    throw new RoleQueryError('System roles cannot be deleted', 'SYSTEM_ROLE');
  }

  // Check if role is assigned to any users
  const { data: assignments } = await supabase
    .from('user_roles')
    .select('id')
    .eq('role_id', roleId)
    .limit(1);

  if (assignments && assignments.length > 0) {
    throw new RoleQueryError('Cannot delete role that is assigned to users', 'ROLE_IN_USE');
  }

  const { error } = await supabase
    .from('roles')
    .delete()
    .eq('id', roleId)
    .eq('organization_id', organizationId);

  if (error) {
    throw new RoleQueryError(`Failed to delete role: ${error.message}`, error.code);
  }

  // Log the role deletion
  await logRoleAction(deletedBy, undefined, roleId, organizationId, 'role_deleted', currentRole.permissions, undefined);
}

/**
 * Get user roles for an organization
 */
export async function getUserRoles(organizationId: string): Promise<UserRole[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_roles')
    .select(`
      *,
      role:roles(*)
    `)
    .eq('organization_id', organizationId)
    .order('assigned_at', { ascending: false });

  if (error) {
    throw new RoleQueryError(`Failed to fetch user roles: ${error.message}`, error.code);
  }

  return (data || []).map(userRole => ({
    id: userRole.id,
    userId: userRole.user_id,
    roleId: userRole.role_id,
    organizationId: userRole.organization_id,
    assignedBy: nullToUndefined(userRole.assigned_by),
    assignedAt: userRole.assigned_at,
    expiresAt: nullToUndefined(userRole.expires_at),
    role: userRole.role ? {
      id: userRole.role.id,
      organizationId: userRole.role.organization_id,
      name: userRole.role.name,
      description: nullToUndefined(userRole.role.description),
      permissions: parsePermissions(userRole.role.permissions),
      isSystemRole: userRole.role.is_system_role,
      createdBy: undefined,
      createdAt: userRole.role.created_at,
      updatedAt: userRole.role.updated_at,
    } : undefined,
  }));
}

/**
 * Get roles for a specific user
 */
export async function getUserRolesByUserId(userId: string, organizationId: string): Promise<UserRole[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_roles')
    .select(`
      *,
      role:roles(*)
    `)
    .eq('user_id', userId)
    .eq('organization_id', organizationId)
    .order('assigned_at', { ascending: false });

  if (error) {
    throw new RoleQueryError(`Failed to fetch user roles: ${error.message}`, error.code);
  }

  return (data || []).map(userRole => ({
    id: userRole.id,
    userId: userRole.user_id,
    roleId: userRole.role_id,
    organizationId: userRole.organization_id,
    assignedBy: nullToUndefined(userRole.assigned_by),
    assignedAt: userRole.assigned_at,
    expiresAt: nullToUndefined(userRole.expires_at),
    role: userRole.role ? {
      id: userRole.role.id,
      organizationId: userRole.role.organization_id,
      name: userRole.role.name,
      description: nullToUndefined(userRole.role.description),
      permissions: parsePermissions(userRole.role.permissions),
      isSystemRole: userRole.role.is_system_role,
      createdBy: undefined,
      createdAt: userRole.role.created_at,
      updatedAt: userRole.role.updated_at,
    } : undefined,
  }));
}

/**
 * Assign a role to a user
 */
export async function assignRole(
  userId: string,
  roleId: string,
  organizationId: string,
  assignedBy: string,
  expiresAt?: string
): Promise<UserRole> {
  const supabase = createClient();
  
  // Check if assignment already exists
  const { data: existing } = await supabase
    .from('user_roles')
    .select('id')
    .eq('user_id', userId)
    .eq('role_id', roleId)
    .eq('organization_id', organizationId)
    .single();

  if (existing) {
    throw new RoleQueryError('User already has this role assigned', 'ALREADY_ASSIGNED');
  }

  const { data, error } = await supabase
    .from('user_roles')
    .insert({
      user_id: userId,
      role_id: roleId,
      organization_id: organizationId,
      assigned_by: assignedBy,
      expires_at: expiresAt,
    })
    .select(`
      *,
      role:roles(*)
    `)
    .single();

  if (error) {
    throw new RoleQueryError(`Failed to assign role: ${error.message}`, error.code);
  }

  // Log the role assignment
  await logRoleAction(assignedBy, userId, roleId, organizationId, 'assigned');

  return {
    id: data.id,
    userId: data.user_id,
    roleId: data.role_id,
    organizationId: data.organization_id,
    assignedBy: nullToUndefined(data.assigned_by),
    assignedAt: data.assigned_at,
    expiresAt: nullToUndefined(data.expires_at),
    role: data.role ? {
      id: data.role.id,
      organizationId: data.role.organization_id,
      name: data.role.name,
      description: nullToUndefined(data.role.description),
      permissions: parsePermissions(data.role.permissions),
      isSystemRole: data.role.is_system_role,
      createdBy: undefined,
      createdAt: data.role.created_at,
      updatedAt: data.role.updated_at,
    } : undefined,
  };
}

/**
 * Remove a role from a user
 */
export async function removeRole(
  userId: string,
  roleId: string,
  organizationId: string,
  removedBy: string
): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('user_roles')
    .delete()
    .eq('user_id', userId)
    .eq('role_id', roleId)
    .eq('organization_id', organizationId);

  if (error) {
    throw new RoleQueryError(`Failed to remove role: ${error.message}`, error.code);
  }

  // Log the role removal
  await logRoleAction(removedBy, userId, roleId, organizationId, 'removed');
}

/**
 * Get user permissions for a specific user
 */
export async function getUserPermissions(userId: string, organizationId: string): Promise<Permission[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_roles')
    .select(`
      role:roles(permissions)
    `)
    .eq('user_id', userId)
    .eq('organization_id', organizationId);

  if (error) {
    throw new RoleQueryError(`Failed to fetch user permissions: ${error.message}`, error.code);
  }

  const permissions: Permission[] = [];
  
  for (const userRole of data || []) {
    if (userRole.role?.permissions) {
      const rolePermissions = parsePermissions(userRole.role.permissions);
      permissions.push(...rolePermissions);
    }
  }
  
  // Remove duplicates
  const uniquePermissions = permissions.filter((permission, index) => {
    return permissions.findIndex(p => 
      p.resource === permission.resource && 
      p.action === permission.action &&
      p.scope === permission.scope
    ) === index;
  });
  
  return uniquePermissions;
}

/**
 * Get audit log entries
 */
export async function getRoleAuditLog(
  organizationId: string,
  limit: number = 50,
  offset: number = 0
): Promise<RoleAuditEntry[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('role_audit_log')
    .select('*')
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new RoleQueryError(`Failed to fetch audit log: ${error.message}`, error.code);
  }

  return (data || []).map(entry => ({
    id: entry.id,
    userId: entry.user_id,
    targetUserId: nullToUndefined(entry.target_user_id),
    roleId: nullToUndefined(entry.role_id),
    organizationId: entry.organization_id,
    action: entry.action as RoleAuditEntry['action'],
    oldPermissions: parsePermissions(entry.old_permissions),
    newPermissions: parsePermissions(entry.new_permissions),
    metadata: (typeof entry.metadata === 'object' && entry.metadata !== null) ? entry.metadata as Record<string, any> : {},
    createdAt: entry.created_at,
  }));
}

/**
 * Log a role-related action for audit purposes
 */
export async function logRoleAction(
  userId: string,
  targetUserId?: string,
  roleId?: string,
  organizationId?: string,
  action?: string,
  oldPermissions?: Permission[],
  newPermissions?: Permission[],
  metadata?: Record<string, any>
): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('role_audit_log')
    .insert({
      user_id: userId,
      target_user_id: targetUserId || null,
      role_id: roleId || null,
      organization_id: organizationId || '',
      action: action || '',
      old_permissions: oldPermissions as any,
      new_permissions: newPermissions as any,
      metadata: metadata || {},
    });

  if (error) {
    // Don't throw here as audit logging should not break the main operation
    console.error('Failed to log role action:', error);
  }
} 