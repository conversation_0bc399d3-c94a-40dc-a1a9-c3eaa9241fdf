export interface TenantInfo {
  organizationId: string;
  slug: string;
  customDomain?: string;
  subscriptionStatus: 'trial' | 'active' | 'cancelled' | 'past_due';
  plan: string | null;
  features: string[];
  trialEndsAt?: Date;
  isActive: boolean;
}

export interface TenantContext {
  tenant: TenantInfo | null;
  isLoading: boolean;
  error?: string;
}

export interface TenantDetectionResult {
  type: 'subdomain' | 'custom_domain' | 'none';
  value?: string;
  organization?: TenantInfo;
}

export interface TenantMiddlewareConfig {
  requireTenant?: boolean;
  allowedPlans?: string[];
  requiredFeatures?: string[];
  redirectTo?: string;
}

export interface TenantRequestContext {
  tenant: TenantInfo;
  user: {
    id: string;
    role: 'owner' | 'admin' | 'manager' | 'staff';
    permissions: string[];
  };
}

// Feature flags for different subscription plans
export const PLAN_FEATURES = {
  'trial': ['basic_menu', 'up_to_5_items'],
  'starter': ['basic_menu', 'up_to_50_items', 'custom_domain'],
  'professional': ['advanced_menu', 'unlimited_items', 'custom_domain', 'analytics', 'team_members'],
  'enterprise': ['advanced_menu', 'unlimited_items', 'custom_domain', 'analytics', 'team_members', 'api_access', 'white_label']
} as const;

export type PlanType = keyof typeof PLAN_FEATURES;
export type FeatureType = typeof PLAN_FEATURES[PlanType][number]; 