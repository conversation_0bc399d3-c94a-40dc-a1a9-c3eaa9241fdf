-- Enable Row Level Security on all tenant-related tables

-- Organizations table RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see organizations they are members of
CREATE POLICY "Users can only access their organizations" ON organizations
  FOR ALL USING (
    id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );

-- Users table RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can see other users in their organization
CREATE POLICY "Users can see organization members" ON users
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );

-- Policy: Users can update their own profile
CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (id = auth.uid());

-- Policy: Organization owners and admins can manage users
CREATE POLICY "Owners and admins can manage users" ON users
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = true
    )
  );

-- Menus table RLS
ALTER TABLE menus ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access menus from their organization
CREATE POLICY "Users can access organization menus" ON menus
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );

-- Menu Categories table RLS
ALTER TABLE menu_categories ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access categories from their organization's menus
CREATE POLICY "Users can access organization menu categories" ON menu_categories
  FOR ALL USING (
    menu_id IN (
      SELECT m.id 
      FROM menus m
      JOIN users u ON u.organization_id = m.organization_id
      WHERE u.id = auth.uid() AND u.is_active = true
    )
  );

-- Menu Items table RLS
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access items from their organization's menu categories
CREATE POLICY "Users can access organization menu items" ON menu_items
  FOR ALL USING (
    category_id IN (
      SELECT mc.id 
      FROM menu_categories mc
      JOIN menus m ON m.id = mc.menu_id
      JOIN users u ON u.organization_id = m.organization_id
      WHERE u.id = auth.uid() AND u.is_active = true
    )
  );

-- Subscriptions table RLS
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access their organization's subscription
CREATE POLICY "Users can access organization subscription" ON subscriptions
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );

-- Subscription Items table RLS
ALTER TABLE subscription_items ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access subscription items for their organization
CREATE POLICY "Users can access organization subscription items" ON subscription_items
  FOR ALL USING (
    subscription_id IN (
      SELECT s.id 
      FROM subscriptions s
      JOIN users u ON u.organization_id = s.organization_id
      WHERE u.id = auth.uid() AND u.is_active = true
    )
  );

-- Invoices table RLS
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Policy: Users can access their organization's invoices
CREATE POLICY "Users can access organization invoices" ON invoices
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON organizations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON menus TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON menu_categories TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON menu_items TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON subscriptions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON subscription_items TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON invoices TO authenticated;

-- Create function to get current user's organization
CREATE OR REPLACE FUNCTION get_user_organization_id()
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT organization_id 
  FROM users 
  WHERE id = auth.uid() AND is_active = true
  LIMIT 1;
$$;

-- Create function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_permission(permission_name text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    CASE 
      WHEN role = 'owner' THEN true
      WHEN role = 'admin' AND permission_name != 'delete_organization' THEN true
      WHEN permissions ? permission_name THEN true
      WHEN permissions ? '*' THEN true
      ELSE false
    END
  FROM users 
  WHERE id = auth.uid() AND is_active = true
  LIMIT 1;
$$;

-- Create function to validate organization membership
CREATE OR REPLACE FUNCTION is_organization_member(org_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM users 
    WHERE id = auth.uid() 
      AND organization_id = org_id 
      AND is_active = true
  );
$$;

-- Grant execution on functions
GRANT EXECUTE ON FUNCTION get_user_organization_id() TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_permission(text) TO authenticated;
GRANT EXECUTE ON FUNCTION is_organization_member(uuid) TO authenticated; 