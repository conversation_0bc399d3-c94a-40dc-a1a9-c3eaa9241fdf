# 🔧 Supabase Connection Fix Guide

Your database is working perfectly! The issue is just missing environment variables. Follow these steps:

## ✅ Database Status: WORKING

- ✅ All tables created successfully
- ✅ Seed data loaded (3 organizations, menus, categories, items)
- ✅ Row Level Security enabled
- ✅ MCP connection confirmed

## 🔨 Fix Steps

### Step 1: Create `.env.local` file

Create a new file called `.env.local` in your project root:

```bash
# Database Configuration (Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_key_here

# Development
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
```

### Step 2: Get Your Supabase Credentials

1. Go to [supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Project Settings** → **API**
4. Copy these values:
   - **Project URL** → `NEXT_PUBLIC_SUPABASE_URL`
   - **anon/public key** → `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - **service_role key** → `SUPABASE_SERVICE_ROLE_KEY`

### Step 3: Restart Development Server

```bash
npm run dev
```

### Step 4: Test the Connection

Visit `http://localhost:3000/test-db` - you should now see:

- ✅ Connection Successful
- ✅ 3 organizations displayed
- ✅ All tables accessible

## 🎯 What Was Fixed

1. **Removed deprecated package**: Uninstalled `@supabase/auth-helpers-nextjs`
2. **Updated server config**: Fixed cookie methods to use `get`, `set`, `remove`
3. **Environment setup**: Need to add actual Supabase credentials

## 🗃️ Your Current Database

Your database already contains this seed data:

### Organizations

- **Pizza Palace** (trial/starter) - Italian cuisine
- **Burger Central** (active/professional) - Gourmet burgers
- **Sushi Express** (trial) - Japanese cuisine

### Menu Items

- Pizza Palace: Margherita Pizza ($16.99), Pepperoni Pizza ($18.99), Spaghetti Carbonara ($19.99), Bruschetta ($8.99)
- Burger Central: Classic Cheeseburger ($15.99), Truffle Fries ($9.99)

## 🔒 Security Features Enabled

- Row Level Security on all tables
- Multi-tenant data isolation
- Public menu access for customers
- Role-based access control
- Service role bypass for admin operations

After completing these steps, your application will connect successfully to the database!
