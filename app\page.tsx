import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  Users,
  Globe,
  CreditCard,
  BarChart3,
  Smartphone,
  Shield,
  ArrowRight,
  Play,
  User,
  Star,
  Sparkles
} from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-25 via-white to-green-50 relative overflow-hidden">
      {/* Beautiful background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-orange-400/10 to-red-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"></div>
      </div>
                      {/* Header */}
        <header className="w-full px-2 sm:px-4 py-4 lg:py-6 relative z-10">
          <div className="container mx-auto max-w-[98vw]">
            <nav className="flex items-center justify-between backdrop-blur-sm bg-white/30 rounded-2xl px-3 sm:px-6 py-3 sm:py-4 shadow-lg border border-white/20">
              <div className="flex items-center space-x-2 min-w-0 flex-shrink">
                <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-bold text-sm sm:text-lg">R</span>
                </div>
                <span className="text-lg sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent truncate">
                  RestaurantSaaS
                </span>
              </div>
            
              <div className="hidden lg:flex items-center space-x-6">
                <Link href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Features
                </Link>
                <Link href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Pricing
                </Link>
                <Link href="#about" className="text-gray-600 hover:text-gray-900 transition-colors">
                  About
                </Link>
              </div>

              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <Link href="/login">
                  <Button variant="outline" className="text-xs sm:text-sm px-2 py-1.5 sm:px-3 sm:py-2 border-white/30 backdrop-blur-sm bg-white/20 hover:bg-white/40 transition-all duration-300 min-w-0">
                    <User className="h-3 w-3 sm:h-4 sm:w-4 lg:hidden" />
                    <span className="hidden lg:inline text-sm">Sign In</span>
                  </Button>
                </Link>
                <Link href="/register">
                  <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-xs sm:text-sm px-2 py-1.5 sm:px-3 sm:py-2 shadow-lg hover:shadow-xl transition-all duration-300 whitespace-nowrap">
                    <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    <span className="hidden xs:inline">Get Started</span>
                    <span className="xs:hidden">Start</span>
                  </Button>
                </Link>
              </div>
            </nav>
          </div>
        </header>

                      {/* Hero Section */}
        <section className="w-full px-2 sm:px-4 py-8 sm:py-12 lg:py-20 relative z-10">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="secondary" className="mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium bg-gradient-to-r from-orange-100 to-red-100 border-orange-200 text-orange-800 shadow-lg">
                <Star className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-orange-600" />
                🎉 Launch Special: 50% off first 3 months!
              </Badge>
            
              <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-4 sm:mb-6 px-2">
                Your Restaurant&apos;s
                <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent block">
                  Digital Menu
                </span>
                Revolution
              </h1>
            
              <p className="text-base sm:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-2">
                Create stunning online menus with custom domains, subscription management, 
                and analytics. Perfect for modern restaurants ready to scale.
              </p>

              <div className="flex flex-col gap-3 sm:gap-4 mb-8 sm:mb-12 px-2">
                <Link href="/register" className="w-full sm:w-auto">
                  <Button size="lg" className="w-full sm:w-auto bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300">
                    Start Free Trial
                    <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 backdrop-blur-sm bg-white/30 border-white/30 hover:bg-white/50 shadow-xl transition-all duration-300">
                  <Play className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Watch Demo
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-8 text-xs sm:text-sm text-gray-500 px-2">
                <div className="flex items-center">
                  <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-green-500" />
                  Free 14-day trial
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-green-500" />
                  No credit card required
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-green-500" />
                  Cancel anytime
                </div>
              </div>
            </div>
          </div>
        </section>

              {/* Social Proof */}
        <section className="w-full px-2 sm:px-4 py-8 sm:py-12 relative z-10">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-6 sm:mb-8">
              <p className="text-sm sm:text-base text-gray-600 font-medium">Trusted by restaurants worldwide</p>
            </div>
            <div className="flex items-center justify-center gap-4 sm:gap-8 lg:gap-12 opacity-60 overflow-x-auto px-2">
              {/* Placeholder for restaurant logos */}
              <div className="h-8 w-16 sm:h-12 sm:w-24 bg-gray-200 rounded flex-shrink-0"></div>
              <div className="h-8 w-16 sm:h-12 sm:w-24 bg-gray-200 rounded flex-shrink-0"></div>
              <div className="h-8 w-16 sm:h-12 sm:w-24 bg-gray-200 rounded flex-shrink-0"></div>
              <div className="h-8 w-16 sm:h-12 sm:w-24 bg-gray-200 rounded flex-shrink-0"></div>
            </div>
          </div>
        </section>

              {/* Features Section */}
        <section id="features" className="container mx-auto px-4 py-16 lg:py-24 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
              Everything your restaurant needs
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From beautiful menu design to advanced analytics, we&apos;ve got you covered
            </p>
          </div>

                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <div className="h-12 w-12 bg-gradient-to-r from-orange-100 to-red-100 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                    <feature.icon className="h-6 w-6 text-orange-600" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="w-full px-2 sm:px-4 py-12 sm:py-16 lg:py-24 bg-gray-50 relative z-10">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-5xl font-bold mb-4 px-2">
              Simple, transparent pricing
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
              Choose the plan that works for your restaurant size
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-5xl mx-auto px-2">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`relative ${plan.popular ? 'border-orange-500 border-2' : ''} h-full flex flex-col`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-gradient-to-r from-orange-500 to-red-500 text-xs sm:text-sm">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="flex-shrink-0">
                  <CardTitle className="text-xl sm:text-2xl">{plan.name}</CardTitle>
                  <CardDescription className="text-sm sm:text-base text-gray-600">{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-2xl sm:text-3xl lg:text-4xl font-bold">${plan.price}</span>
                    <span className="text-sm sm:text-base text-gray-600">/month</span>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow flex flex-col">
                  <ul className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 flex-grow">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-sm sm:text-base text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full text-sm sm:text-base py-2 sm:py-3 ${plan.popular ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600' : ''} mt-auto`}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="h-8 w-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">R</span>
                </div>
                <span className="text-xl font-bold">RestaurantSaaS</span>
              </div>
              <p className="text-gray-400">
                Empowering restaurants with modern digital menu solutions.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Demo</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">API Docs</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Careers</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 RestaurantSaaS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

const features = [
  {
    icon: Globe,
    title: "Custom Domains",
    description: "Use your own domain name for a professional restaurant website"
  },
  {
    icon: Smartphone,
    title: "Mobile Optimized",
    description: "Beautiful, responsive menus that look perfect on any device"
  },
  {
    icon: BarChart3,
    title: "Analytics Dashboard",
    description: "Track menu performance, popular items, and customer insights"
  },
  {
    icon: CreditCard,
    title: "Subscription Management",
    description: "Flexible pricing plans with automatic billing and upgrades"
  },
  {
    icon: Users,
    title: "Team Management",
    description: "Invite staff members with role-based access control"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level security with data encryption and backups"
  }
]

const pricingPlans = [
  {
    name: "Starter",
    description: "Perfect for small restaurants",
    price: 29,
    popular: false,
    features: [
      "1 Restaurant Location",
      "Up to 50 Menu Items",
      "Custom Subdomain",
      "Basic Analytics",
      "Email Support"
    ]
  },
  {
    name: "Professional",
    description: "Most popular for growing restaurants",
    price: 79,
    popular: true,
    features: [
      "Up to 3 Locations",
      "Unlimited Menu Items",
      "Custom Domain",
      "Advanced Analytics",
      "Team Management",
      "Priority Support"
    ]
  },
  {
    name: "Enterprise",
    description: "For restaurant chains",
    price: 199,
    popular: false,
    features: [
      "Unlimited Locations",
      "White-label Solution",
      "API Access",
      "Custom Integrations",
      "Dedicated Account Manager",
      "24/7 Phone Support"
    ]
  }
] 