import { Permission } from '@/types/roles';
import { getUserPermissions } from '@/lib/db/role-queries';

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  userPermissions: Permission[],
  requiredPermission: Permission
): boolean {
  return userPermissions.some(
    (p) =>
      p.resource === requiredPermission.resource &&
      p.action === requiredPermission.action &&
      (p.scope === requiredPermission.scope || !requiredPermission.scope)
  );
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  userPermissions: Permission[],
  requiredPermissions: Permission[]
): boolean {
  return requiredPermissions.some(permission => 
    hasPermission(userPermissions, permission)
  );
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(
  userPermissions: Permission[],
  requiredPermissions: Permission[]
): boolean {
  return requiredPermissions.every(permission => 
    hasPermission(userPermissions, permission)
  );
}

/**
 * Filter permissions by resource
 */
export function getPermissionsForResource(
  userPermissions: Permission[],
  resource: string
): Permission[] {
  return userPermissions.filter(p => p.resource === resource);
}

/**
 * Get all available actions for a resource
 */
export function getActionsForResource(
  userPermissions: Permission[],
  resource: string
): string[] {
  return userPermissions
    .filter(p => p.resource === resource)
    .map(p => p.action);
}

/**
 * Check if a user can perform an action on a resource
 */
export function canPerformAction(
  userPermissions: Permission[],
  resource: string,
  action: string,
  scope?: 'own' | 'team' | 'organization'
): boolean {
  return hasPermission(userPermissions, { resource, action, scope });
}

/**
 * Server-side permission checking for a specific user
 */
export async function checkUserPermission(
  userId: string,
  organizationId: string,
  requiredPermission: Permission
): Promise<boolean> {
  try {
    const userPermissions = await getUserPermissions(userId, organizationId);
    return hasPermission(userPermissions, requiredPermission);
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}

/**
 * Server-side check for multiple permissions
 */
export async function checkUserPermissions(
  userId: string,
  organizationId: string,
  requiredPermissions: Permission[],
  requireAll: boolean = false
): Promise<boolean> {
  try {
    const userPermissions = await getUserPermissions(userId, organizationId);
    
    if (requireAll) {
      return hasAllPermissions(userPermissions, requiredPermissions);
    } else {
      return hasAnyPermission(userPermissions, requiredPermissions);
    }
  } catch (error) {
    console.error('Error checking user permissions:', error);
    return false;
  }
}

/**
 * Middleware helper for API route protection
 */
export function requirePermission(permission: Permission) {
  return async (userId: string, organizationId: string): Promise<boolean> => {
    return await checkUserPermission(userId, organizationId, permission);
  };
}

/**
 * Get user's role-based capabilities
 */
export async function getUserCapabilities(
  userId: string,
  organizationId: string
): Promise<{
  canManageRoles: boolean;
  canManageUsers: boolean;
  canManageOrganization: boolean;
  canManageMenus: boolean;
  canViewAnalytics: boolean;
  permissions: Permission[];
}> {
  const permissions = await getUserPermissions(userId, organizationId);
  
  return {
    canManageRoles: canPerformAction(permissions, 'role', 'create') || 
                   canPerformAction(permissions, 'role', 'manage'),
    canManageUsers: canPerformAction(permissions, 'user', 'manage'),
    canManageOrganization: canPerformAction(permissions, 'organization', 'manage'),
    canManageMenus: canPerformAction(permissions, 'menu', 'manage') ||
                   canPerformAction(permissions, 'menu', 'create'),
    canViewAnalytics: canPerformAction(permissions, 'analytics', 'read'),
    permissions,
  };
} 