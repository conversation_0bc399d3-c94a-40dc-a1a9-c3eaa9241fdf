import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { detectTenant } from '@/lib/tenant/detection'
import { validateTenant, validateOrganizationMembership } from '@/lib/tenant/validation'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/organization',
  '/settings',
  '/profile',
  '/onboarding',
]

// Define auth routes that should redirect authenticated users
const authRoutes = [
  '/login',
  '/register',
  '/reset-password',
]

// Define tenant-specific routes that require organization context
const tenantRoutes = [
  '/menus',
  '/orders',
  '/analytics',
  '/team',
]

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Authenticate user - required for Server Components
  const { data: { user } } = await supabase.auth.getUser()
  const session = user ? { user } : null

  // Skip middleware for auth API routes (Supabase auth)
  if (request.nextUrl.pathname.startsWith('/auth/')) {
    return response
  }

  const isProtectedRoute = protectedRoutes.some(route => 
    request.nextUrl.pathname.startsWith(route)
  )
  const isAuthRoute = authRoutes.some(route => 
    request.nextUrl.pathname.startsWith(route)
  )
  const isTenantRoute = tenantRoutes.some(route => 
    request.nextUrl.pathname.startsWith(route)
  )

  // Detect tenant from request
  const host = request.headers.get('host')
  const tenantDetection = await detectTenant(host)

  // Add tenant info to request headers for downstream components
  if (tenantDetection.organization) {
    response.headers.set('x-tenant-id', tenantDetection.organization.organizationId)
    response.headers.set('x-tenant-slug', tenantDetection.organization.slug)
    response.headers.set('x-tenant-type', tenantDetection.type)
  }

  // If accessing protected route without session, redirect to login
  if (isProtectedRoute && !session) {
    const redirectUrl = new URL('/login', request.url)
    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If accessing auth routes with session, redirect to dashboard
  if (isAuthRoute && session) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // Handle tenant-specific routes
  if (isTenantRoute && session) {
    // Check if tenant exists and is valid
    if (tenantDetection.type === 'none') {
      // No tenant detected - in development mode, allow access without tenant
      const host = request.headers.get('host') || ''
      const isDevelopment = host.includes('localhost') || host.includes('127.0.0.1')
      
      if (!isDevelopment) {
        // In production, redirect to organization selection or main app
        return NextResponse.redirect(new URL('/organization', request.url))
      }
      // In development, continue without tenant validation
      return response
    }

    if (!tenantDetection.organization) {
      // Tenant not found
      return NextResponse.redirect(new URL('/404', request.url))
    }

    // Validate tenant status
    const tenantValidation = await validateTenant(tenantDetection.organization)
    if (!tenantValidation.isValid) {
      // Tenant is inactive - redirect to subscription page or error
      const errorUrl = new URL('/subscription-required', request.url)
      errorUrl.searchParams.set('error', tenantValidation.error || 'Subscription required')
      return NextResponse.redirect(errorUrl)
    }

    // Validate user's membership in the organization
    const membership = await validateOrganizationMembership(
      session.user.id,
      tenantDetection.organization.organizationId
    )

    if (!membership.isMember) {
      // User is not a member of this organization
      return NextResponse.redirect(new URL('/unauthorized', request.url))
    }

    if (!membership.isActive) {
      // User's membership is inactive
      return NextResponse.redirect(new URL('/account-suspended', request.url))
    }

    // Add user context to headers
    response.headers.set('x-user-role', membership.role || 'staff')
    response.headers.set('x-organization-id', tenantDetection.organization.organizationId)
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images/ (image files)
     * - api/ (API routes - handled separately)
     */
    '/((?!_next/static|_next/image|favicon.ico|images/|api/).*)',
  ],
} 