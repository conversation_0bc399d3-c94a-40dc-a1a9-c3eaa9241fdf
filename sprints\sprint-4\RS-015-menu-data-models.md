# RS-015: Menu Data Models

## Ticket Information

- **Story:** 4.1 - Menu Builder Interface
- **Priority:** High
- **Assignee:** Backend Developer
- **Estimate:** 5 points
- **Status:** 📋 **OPEN**
- **Sprint:** 4 - Menu Management Foundation

## Description

Create comprehensive data models and API endpoints for menu management.

## Technical Tasks

### 🔄 Pending Tasks

- [ ] Create TypeScript interfaces in `types/menu.ts`:

  - `Menu` interface
  - `MenuCategory` interface
  - `MenuItem` interface
  - `MenuFormData` interface

- [ ] Create `app/api/menus/route.ts`:

  - GET: Fetch organization menus
  - POST: Create new menu
  - PUT: Update menu
  - DELETE: Remove menu

- [ ] Create `app/api/menus/[menuId]/categories/route.ts`:

  - CRUD operations for categories
  - Category reordering
  - Bulk operations

- [ ] Create `app/api/menus/[menuId]/items/route.ts`:

  - CRUD operations for menu items
  - Item reordering
  - Bulk operations

- [ ] Add validation schemas with Zod:

  - Menu validation schema
  - Category validation schema
  - Item validation schema
  - Price validation

- [ ] Implement database queries:
  - Optimized menu fetching
  - Hierarchical data loading
  - Search and filtering
  - Performance optimizations

## Acceptance Criteria

- [ ] All API endpoints function correctly
- [ ] Data validation prevents invalid entries
- [ ] Database queries are optimized
- [ ] TypeScript interfaces provide type safety
- [ ] CRUD operations work for all menu entities
- [ ] Error handling is comprehensive

## Definition of Done

- [ ] Code is peer-reviewed and approved
- [ ] All acceptance criteria are met
- [ ] Unit tests are written and passing
- [ ] Integration tests pass
- [ ] Security requirements are satisfied
- [ ] Performance requirements are met
- [ ] Documentation is updated
- [ ] QA testing is complete

## Dependencies

- ✅ RS-008 (User Roles and Permissions System) - **COMPLETED**

## Database Schema

```sql
-- Menus table
CREATE TABLE menus (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  is_published BOOLEAN DEFAULT false,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Menu Categories table
CREATE TABLE menu_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  menu_id UUID NOT NULL REFERENCES menus(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Menu Items table
CREATE TABLE menu_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category_id UUID NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  image_url TEXT,
  allergens TEXT[] DEFAULT '{}',
  dietary_info TEXT[] DEFAULT '{}',
  is_available BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_menus_organization_id ON menus(organization_id);
CREATE INDEX idx_menu_categories_menu_id ON menu_categories(menu_id);
CREATE INDEX idx_menu_items_category_id ON menu_items(category_id);
CREATE INDEX idx_menu_items_available ON menu_items(is_available);
```

## TypeScript Interfaces

```typescript
interface Menu {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  isActive: boolean;
  isPublished: boolean;
  displayOrder: number;
  categories?: MenuCategory[];
  createdAt: string;
  updatedAt: string;
}

interface MenuCategory {
  id: string;
  menuId: string;
  name: string;
  description?: string;
  displayOrder: number;
  isActive: boolean;
  items?: MenuItem[];
  createdAt: string;
  updatedAt: string;
}

interface MenuItem {
  id: string;
  categoryId: string;
  name: string;
  description?: string;
  price: number;
  imageUrl?: string;
  allergens: string[];
  dietaryInfo: string[];
  isAvailable: boolean;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
}
```

## File Structure

```
types/
├── menu.ts
├── category.ts
└── item.ts
app/api/
├── menus/
│   ├── route.ts
│   └── [menuId]/
│       ├── route.ts
│       ├── categories/
│       │   ├── route.ts
│       │   └── [categoryId]/
│       │       └── route.ts
│       └── items/
│           ├── route.ts
│           └── [itemId]/
│               └── route.ts
lib/
├── validations/
│   ├── menu.ts
│   ├── category.ts
│   └── item.ts
├── db/
│   ├── menu-queries.ts
│   ├── category-queries.ts
│   └── item-queries.ts
```

## Validation Schemas

```typescript
// Menu validation
const menuSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
});

// Category validation
const categorySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  displayOrder: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
});

// Item validation
const itemSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(1000).optional(),
  price: z.number().positive().max(9999.99),
  allergens: z.array(z.string()).default([]),
  dietaryInfo: z.array(z.string()).default([]),
  isAvailable: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
});
```

## API Endpoints

### Menu Endpoints

- `GET /api/menus` - List organization menus
- `POST /api/menus` - Create new menu
- `GET /api/menus/[menuId]` - Get menu details
- `PUT /api/menus/[menuId]` - Update menu
- `DELETE /api/menus/[menuId]` - Delete menu

### Category Endpoints

- `GET /api/menus/[menuId]/categories` - List categories
- `POST /api/menus/[menuId]/categories` - Create category
- `PUT /api/menus/[menuId]/categories/[categoryId]` - Update category
- `DELETE /api/menus/[menuId]/categories/[categoryId]` - Delete category

### Item Endpoints

- `GET /api/menus/[menuId]/items` - List all items
- `POST /api/menus/[menuId]/items` - Create item
- `PUT /api/menus/[menuId]/items/[itemId]` - Update item
- `DELETE /api/menus/[menuId]/items/[itemId]` - Delete item

## Security Considerations

- Implement RLS policies for multi-tenant data isolation
- Validate user permissions for menu operations
- Sanitize all user inputs
- Implement rate limiting for API endpoints
- Log all data modifications for audit trail

## Performance Optimizations

- Use database indexes for common queries
- Implement pagination for large datasets
- Cache frequently accessed menu data
- Optimize queries with JOIN operations
- Use connection pooling for database access

## Testing Requirements

- [ ] Unit tests for all API endpoints
- [ ] Integration tests with database
- [ ] Validation schema testing
- [ ] Performance testing with large datasets
- [ ] Security testing for authorization

## Related Stories

- Story 4.1: Menu Builder Interface
- Story 4.2: Menu Item Management (depends on this)

## Next Steps After Completion

1. Create menu builder UI components (RS-016)
2. Implement menu item management (RS-017)
3. Build menu categories system (RS-018)
