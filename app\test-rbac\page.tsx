'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Users, 
  Settings, 
  CheckCircle2,
  Database,
  Lock,
  UserCheck,
  TestTube
} from 'lucide-react';
import Link from 'next/link';

export default function RBACTestPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">RBAC System Test Dashboard</h1>
        <p className="text-gray-600">
          Comprehensive testing interface for the Role-Based Access Control system
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">1</p>
              <p className="text-xs text-gray-600">Total Users</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Shield className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">4</p>
              <p className="text-xs text-gray-600">Total Roles</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Lock className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">4</p>
              <p className="text-xs text-gray-600">System Roles</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Settings className="h-8 w-8 text-orange-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">0</p>
              <p className="text-xs text-gray-600">Custom Roles</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <UserCheck className="h-8 w-8 text-yellow-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">0</p>
              <p className="text-xs text-gray-600">Pending Invites</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Database className="h-8 w-8 text-red-500 mr-3" />
            <div>
              <p className="text-sm font-bold">Active</p>
              <p className="text-xs text-gray-600">Database</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              System Status
            </CardTitle>
            <CardDescription>
              Current status of RBAC system components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Database Tables</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>System Roles</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Initialized
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>API Endpoints</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>UI Components</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Functional
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Navigate to RBAC management pages
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/dashboard/team">
              <Button variant="outline" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                Team Management
              </Button>
            </Link>
            <Link href="/dashboard/roles">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="h-4 w-4 mr-2" />
                Role Management
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
          <CardDescription>
            Follow these steps to test the RBAC system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">1. Team Management Testing</h3>
              <p className="text-sm text-gray-600 mb-2">
                Navigate to the Team Management page to test user invitations and role assignments.
              </p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>View current team members</li>
                <li>Send team invitations</li>
                <li>Manage member roles</li>
                <li>Check invitation status</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">2. Role Management Testing</h3>
              <p className="text-sm text-gray-600 mb-2">
                Navigate to the Role Management page to test role creation and permission management.
              </p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>View system roles</li>
                <li>Create custom roles</li>
                <li>Edit role permissions</li>
                <li>Test permission validation</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-2">3. API Testing</h3>
              <p className="text-sm text-gray-600 mb-2">
                Test the RBAC API endpoints using curl or your preferred tool.
              </p>
              <div className="bg-gray-50 p-3 rounded text-xs font-mono">
                <p>GET /api/rbac/roles?organizationId=YOUR_ORG_ID</p>
                <p>POST /api/rbac/roles</p>
                <p>GET /api/rbac/team?organizationId=YOUR_ORG_ID</p>
                <p>POST /api/rbac/team</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 