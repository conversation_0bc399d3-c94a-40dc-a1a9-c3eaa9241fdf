import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getUserSettings, updateMultipleSettings } from '@/lib/db/settings-queries';
import { SettingsUpdatePayload } from '@/types/settings';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const settings = await getUserSettings(session.user.id, organizationId);
    return NextResponse.json({ settings });

  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { organizationId, updates } = body as {
      organizationId: string;
      updates: SettingsUpdatePayload[];
    };

    if (!organizationId || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: 'Organization ID and updates array are required' },
        { status: 400 }
      );
    }

    const updatedSettings = await updateMultipleSettings(
      session.user.id,
      organizationId,
      updates
    );

    return NextResponse.json({ 
      success: true, 
      settings: updatedSettings 
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
} 