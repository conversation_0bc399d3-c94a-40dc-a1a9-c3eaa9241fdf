# RS-002: Database Schema Implementation

## Ticket Information

- **Story:** 1.2 - Database Schema Design
- **Priority:** High
- **Assignee:** Backend Developer
- **Estimate:** 8 points
- **Status:** ✅ **COMPLETED** (December 2024)
- **Sprint:** 1 - Foundation & Project Setup

## Description

Design and implement the complete database schema with multi-tenancy support and Row Level Security.

## Technical Tasks

### ✅ Completed Tasks

- [x] ✅ Create `organizations` table with fields:

  - `id` (UUID, primary key)
  - `name` (TEXT, not null)
  - `slug` (TEXT, unique, not null)
  - `custom_domain` (TEXT, unique, nullable)
  - `subscription_status` (TEXT, default 'trial')
  - `plan_id` (TEXT, nullable)
  - `stripe_customer_id` (TEXT, nullable)
  - `trial_ends_at` (TIMESTAMPTZ, nullable)
  - `created_at` (TIMESTAMPTZ)
  - `updated_at` (TIMESTAMPTZ)

- [x] ✅ Create `users` table with fields:

  - `id` (UUID, references auth.users)
  - `email` (TEXT, not null)
  - `full_name` (TEXT, nullable)
  - `avatar_url` (TEXT, nullable)
  - `organization_id` (UUID, references organizations)
  - `role` (TEXT, default 'owner': owner/admin/manager/staff)
  - `permissions` (JSONB)
  - `is_active` (BOOLEAN, default true)
  - `last_login_at` (TIMESTAMPTZ, nullable)
  - `invited_by` (UUID, references users, nullable)
  - `invited_at` (TIMESTAMPTZ, nullable)
  - `accepted_at` (TIMESTAMPTZ, nullable)
  - `created_at` (TIMESTAMPTZ)
  - `updated_at` (TIMESTAMPTZ)

- [x] ✅ Create menu-related tables:

  - **`menus` table** with organization relationships, theme settings, and publishing controls
  - **`menu_categories` table** with ordering, visibility, and image support
  - **`menu_items` table** with pricing, dietary information, allergens, and availability

- [x] ✅ Create `subscriptions` table for Stripe integration with full billing lifecycle
- [x] ✅ Create `subscription_items` table for billing line items
- [x] ✅ Create `invoices` table for payment history and billing management
- [x] ✅ Implement comprehensive Row Level Security (RLS) policies
- [x] ✅ Create database indexes for optimal performance
- [x] ✅ Write database migration scripts with proper naming conventions
- [x] ✅ Create comprehensive seed data for development and testing

## Acceptance Criteria

### ✅ All Criteria Met

- [x] ✅ All tables created with correct relationships and constraints
- [x] ✅ RLS policies prevent cross-tenant data access (multi-tenancy secured)
- [x] ✅ Database migrations run successfully with proper versioning
- [x] ✅ Seed data populates correctly (3 organizations, menus, categories, items)
- [x] ✅ All foreign key constraints work properly and maintain referential integrity

## 🎉 Bonus Achievements

- ✅ **Advanced RLS:** Public menu access policies for customer viewing
- ✅ **Performance Optimized:** Comprehensive indexing strategy for all queries
- ✅ **Rich Data Model:** Support for dietary tags, allergens, and preparation times
- ✅ **Audit Trail:** Updated_at triggers for all tables with automatic timestamps
- ✅ **Stripe Ready:** Complete subscription and billing table structure
- ✅ **Multi-tenant Security:** Organization-based data isolation with role-based access

## Database Schema Details

### Core Tables (8 total)

1. **organizations** - Restaurant companies with subscription management
2. **users** - Team members with role-based permissions
3. **menus** - Restaurant menu containers with theming
4. **menu_categories** - Menu sections with ordering and visibility
5. **menu_items** - Individual dishes with full details
6. **subscriptions** - Stripe subscription lifecycle management
7. **subscription_items** - Billing line items and quantities
8. **invoices** - Payment history and billing records

### Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Multi-tenant isolation** by organization_id
- **Role-based access control** (owner/admin/manager/staff)
- **Public menu access** for customer viewing
- **Service role policies** for webhook operations

### Performance Features

- **Strategic indexing** on all foreign keys and query columns
- **JSONB fields** for flexible metadata storage
- **Automatic timestamps** with updated_at triggers
- **Optimized queries** with proper relationship structures

## Sample Seed Data Created

- **Pizza Palace** (pizza-palace) - Italian restaurant with pizza focus
- **Burger Central** (burger-central) - American fast-casual burger joint
- **Sushi Express** (sushi-express) - Japanese restaurant with sushi specialties

Each with:

- Complete menu structure (categories: Appetizers, Main dishes, Sides)
- 6+ realistic menu items with prices, dietary tags, allergens
- Trial subscription status for development testing

## Dependencies

None

## Related Files Created

- Database migration files with proper naming
- TypeScript type definitions in `types/database.ts`
- Seed data scripts for development
- RLS policy implementations
- Database query utilities in `lib/database/queries.ts`

## Technical Excellence

- Production-ready multi-tenant database architecture
- Comprehensive security with RLS policies
- Performance-optimized with strategic indexing
- Rich data model supporting complex restaurant operations
- Full Stripe integration readiness for subscription billing
