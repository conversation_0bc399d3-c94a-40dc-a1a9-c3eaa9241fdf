import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { resetToDefaults } from '@/lib/db/settings-queries';
import { SettingCategory } from '@/types/settings';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { organizationId, category } = body as {
      organizationId: string;
      category?: SettingCategory;
    };

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    await resetToDefaults(session.user.id, organizationId, category);

    return NextResponse.json({ 
      success: true,
      message: category 
        ? `${category} settings have been reset to defaults`
        : 'All settings have been reset to defaults'
    });

  } catch (error) {
    console.error('Error resetting settings:', error);
    return NextResponse.json(
      { error: 'Failed to reset settings' },
      { status: 500 }
    );
  }
} 